# Workorder Dashboard - Different Charts for Different Views

A comprehensive analytics dashboard that shows different chart types and data based on the selected queue view.

## 🎯 Three Different Chart Views

### 1. Default View (No Queue Selected)
**Dashboard Overview Charts**
- **Priority Summary**: High (8), Medium (12), Low (6) priority tasks
- **System Status Bar Chart**: 
  - Active Tasks: 58
  - Pending Review: 23
  - Completed Today: 15
  - Overdue: 4

### 2. My Queue View (Personal Analytics)
**Personal Task Analytics**
- **Pie Chart**: Visual breakdown of personal tasks (15 total)
  - In Progress: 8 tasks (53%)
  - Created: 4 tasks (27%)
  - Hold: 2 tasks (13%)
  - Move to Tech: 1 task (7%)

- **Priority Breakdown**: Personal priority distribution
  - High Priority: 5 tasks
  - Medium Priority: 7 tasks
  - Low Priority: 3 tasks

- **Task Details Bar Chart**: Detailed view of personal task status

### 3. All Queue View (Comprehensive Analytics)
**Organization-wide Analytics**
- **Stacked Bar Chart**: Complete status distribution (245 total)
  - Created: 23, Move to Tech: 18, In Progress: 35
  - Job Completed by <PERSON>: 25, Completed: 89
  - Assign To Parts Specialist: 12, Hold: 8
  - Closed: 15, Pending For Quote Approval: 14
  - Rejected Warranty Claims: 6

- **Department Workload**: Team distribution
  - Tech Team: 45 tasks
  - Parts Team: 38 tasks
  - Admin Team: 44 tasks

- **Top Categories Bar Chart**: Most active status categories

## 📊 Chart Types and Features

### Pie Chart (My Queue Only)
```css
.pie-chart {
    width: 140px;
    height: 140px;
    position: relative;
}
```
- **SVG-based**: Scalable vector graphics
- **4 Segments**: Personal task breakdown
- **Center Total**: Shows personal task count
- **Color Coded**: Yellow, Gray, Pink, Blue themes

### Stacked Bar Chart (All Queue Only)
```css
.stacked-bar {
    width: 100%;
    height: 20px;
    border-radius: 10px;
}
```
- **Horizontal Layout**: Single bar with segments
- **10 Status Types**: Complete organizational view
- **Proportional Segments**: Width based on percentage
- **Hover Tooltips**: Detailed information on hover

### Priority Charts (All Views)
```css
.priority-chart {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}
```
- **Three Categories**: High, Medium, Low priority
- **Gradient Backgrounds**: Beautiful visual appeal
- **Different Data**: Context-specific numbers
- **Color Themes**: Red, Orange, Green gradients

### Bar Charts (All Views)
```css
.bar-chart {
    display: flex;
    flex-direction: column;
    gap: 6px;
}
```
- **Progress Bars**: Visual percentage indicators
- **Color Dots**: Status identification
- **Count Display**: Exact numbers
- **Responsive**: Adapts to container width

## 🎨 Color Schemes by View

### My Queue Colors
- **In Progress**: #ffc107 (Yellow)
- **Created**: #6c757d (Gray)
- **Hold**: #e91e63 (Pink)
- **Move to Tech**: #1976d2 (Blue)

### All Queue Colors
- **Created**: #6c757d, **Move to Tech**: #1976d2
- **In Progress**: #ffc107, **Job Completed**: #28a745
- **Completed**: #17a2b8, **Parts Specialist**: #6f42c1
- **Hold**: #e91e63, **Closed**: #20c997
- **Quote Approval**: #00bcd4, **Rejected**: #dc3545

### Priority Colors (All Views)
- **High Priority**: Red gradient (#ffebee to #ffcdd2)
- **Medium Priority**: Orange gradient (#fff8e1 to #ffecb3)
- **Low Priority**: Green gradient (#e8f5e8 to #c8e6c9)

## 🔄 Dynamic Data Updates

### Real-time Synchronization
- **30-second Updates**: All charts refresh automatically
- **Data Consistency**: Charts match legend cards
- **Smooth Transitions**: 0.8s ease animations
- **Context Awareness**: Charts change with queue selection

### Data Sources
```javascript
// My Queue Data (15 total)
myQueueData: {
    'In Progress': 8,
    'Created': 4,
    'Hold': 2,
    'Move to Tech': 1
}

// All Queue Data (245 total)
allQueueData: {
    'Created': 23,
    'Move to Tech': 18,
    'In Progress': 35,
    'Job Completed by Tech': 25,
    'Completed': 89,
    'Assign To Parts Specialist': 12,
    'Hold': 8,
    'Closed': 15,
    'Pending For Quote Approval': 14,
    'Rejected Warranty Claims': 6
}
```

## 📱 Responsive Chart Behavior

### Desktop (1200px+)
- **Full Chart Display**: All elements clearly visible
- **Optimal Sizing**: 140px pie chart, 20px stacked bar
- **Complete Labels**: Full status names displayed

### Tablet (1024px - 1199px)
- **Reduced Chart Width**: 300px container
- **Maintained Functionality**: All charts still functional
- **Adjusted Spacing**: Optimized for medium screens

### Mobile (768px and below)
- **Stacked Layout**: Charts below main content
- **Smaller Charts**: 100px pie chart
- **Compact Bars**: Reduced height and spacing
- **Touch Optimized**: Larger touch targets

## 🚀 Performance Features

### Efficient Rendering
- **SVG Charts**: Vector-based for crisp display
- **CSS Animations**: Hardware accelerated
- **Smart Updates**: Only changed elements refresh
- **Memory Efficient**: Proper cleanup and management

### Chart Generation
```javascript
function generateCharts() {
    const legendCards = document.getElementById('legendCards');
    
    if (legendCards.style.display === 'none') {
        generateSummaryCharts();
    } else if (legendCards.classList.contains('my-queue')) {
        generateMyQueueCharts();
    } else {
        generateAllQueueCharts();
    }
}
```

## 🎯 Key Benefits

### Context-Aware Analytics
- **Personal View**: Focus on individual productivity
- **Team View**: Comprehensive organizational insights
- **Summary View**: Quick overview for decision making

### Visual Clarity
- **Different Chart Types**: Best visualization for each context
- **Appropriate Data**: Relevant numbers for each view
- **Color Consistency**: Logical color schemes
- **Professional Design**: Clean, modern appearance

### User Experience
- **Instant Switching**: Charts change immediately with queue selection
- **Relevant Information**: Context-specific data display
- **Easy Understanding**: Intuitive visual representations
- **Responsive Design**: Works on all devices

Perfect for comprehensive workorder analytics with context-aware visualizations!
