<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workorder Dashboard</title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
        }

        /* Header Styles */
        .dashboard-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .last-updated {
            color: #6b7280;
            font-size: 0.875rem;
            background: #f3f4f6;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: 1px solid #e5e7eb;
        }

        /* Main Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        @media (min-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 350px 1fr;
            }
        }

        /* Section Styles */
        .dashboard-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .dashboard-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Queue Counter Styles */
        .queue-counters {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .queue-counter {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--accent-color, #3B82F6)10, transparent);
            border: 2px solid var(--accent-color, #3B82F6)20;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .queue-counter::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color, #3B82F6);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
        }

        .queue-counter:hover::before {
            transform: scaleX(1);
        }

        .queue-counter:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px -4px rgba(0, 0, 0, 0.15);
            border-color: var(--accent-color, #3B82F6);
        }

        .queue-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: var(--accent-color, #3B82F6)15;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .queue-info {
            flex: 1;
        }

        .queue-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .queue-count {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent-color, #3B82F6);
            line-height: 1;
        }

        .queue-trend {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .trend-indicator {
            font-size: 1.5rem;
            color: #10b981;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-4px); }
            60% { transform: translateY(-2px); }
        }

        /* Status Legend Styles */
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: var(--status-bg, #f9fafb);
            border: 1px solid var(--status-color, #e5e7eb)30;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .status-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px -2px var(--status-color, #000)20;
            border-color: var(--status-color, #e5e7eb);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--status-color, #6b7280);
            flex-shrink: 0;
            position: relative;
        }

        .status-indicator::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .status-item:hover .status-indicator::after {
            opacity: 1;
        }

        .status-info {
            flex: 1;
        }

        .status-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.25rem;
            line-height: 1.2;
        }

        .status-metrics {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-count {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--status-color, #6b7280);
        }

        .status-percentage {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
        }

        .status-progress {
            width: 60px;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .progress-bar {
            height: 100%;
            background: var(--status-color, #6b7280);
            border-radius: 2px;
            transition: width 0.8s ease;
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Status Summary */
        .status-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .summary-label {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0.5rem;
            }

            .dashboard-header {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
            }

            .dashboard-title {
                font-size: 1.5rem;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }

            .status-summary {
                flex-direction: column;
                gap: 1rem;
            }

            .summary-item {
                flex-direction: row;
                gap: 0.5rem;
            }
        }

        /* Animation for status items */
        .status-item {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .status-item:nth-child(1) { animation-delay: 0.1s; }
        .status-item:nth-child(2) { animation-delay: 0.2s; }
        .status-item:nth-child(3) { animation-delay: 0.3s; }
        .status-item:nth-child(4) { animation-delay: 0.4s; }
        .status-item:nth-child(5) { animation-delay: 0.5s; }
        .status-item:nth-child(6) { animation-delay: 0.6s; }
        .status-item:nth-child(7) { animation-delay: 0.7s; }
        .status-item:nth-child(8) { animation-delay: 0.8s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading Animation */
        .loading {
            opacity: 0.7;
        }

        .loading .queue-count {
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Dashboard Header -->
        <header class="dashboard-header">
            <h1 class="dashboard-title">Workorder Dashboard</h1>
            <div class="last-updated" id="lastUpdated">
                Last updated: <span id="updateTime"></span>
            </div>
        </header>

        <!-- Main Dashboard Content -->
        <div class="dashboard-grid">
            <!-- Queue Section -->
            <section class="dashboard-section">
                <h2 class="section-title">
                    📊 Queue Overview
                </h2>
                <div class="queue-counters">
                    <!-- My Queue Counter -->
                    <div class="queue-counter" style="--accent-color: #3B82F6;">
                        <div class="queue-icon">👤</div>
                        <div class="queue-info">
                            <div class="queue-title">My Queue</div>
                            <div class="queue-count" id="myQueueCount">15</div>
                        </div>
                        <div class="queue-trend">
                            <span class="trend-indicator">↗</span>
                        </div>
                    </div>

                    <!-- All Queue Counter -->
                    <div class="queue-counter" style="--accent-color: #10B981;">
                        <div class="queue-icon">📋</div>
                        <div class="queue-info">
                            <div class="queue-title">All Queue</div>
                            <div class="queue-count" id="allQueueCount">127</div>
                        </div>
                        <div class="queue-trend">
                            <span class="trend-indicator">↗</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Status Section -->
            <section class="dashboard-section">
                <h2 class="section-title">
                    📈 Status Overview
                </h2>
                <div class="status-grid" id="statusGrid">
                    <!-- Status items will be dynamically generated -->
                </div>

                <div class="status-summary">
                    <div class="summary-item">
                        <span class="summary-label">Total Workorders</span>
                        <span class="summary-value" id="totalWorkorders">205</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Active Statuses</span>
                        <span class="summary-value" id="activeStatuses">8</span>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Dashboard Data and Configuration
        const dashboardData = {
            myQueue: 15,
            allQueue: 127,
            statusCounts: {
                'Created': 23,
                'Move to Tech': 18,
                'In Progress': 35,
                'Assign To Parts Specialist': 12,
                'Completed': 89,
                'Hold': 8,
                'Pending For Quote Approval': 14,
                'Rejected Warranty Claims': 6
            }
        };

        const statusConfig = [
            { name: 'Created', color: '#3B82F6', bgColor: '#EFF6FF' },
            { name: 'Move to Tech', color: '#8B5CF6', bgColor: '#F3E8FF' },
            { name: 'In Progress', color: '#F59E0B', bgColor: '#FFFBEB' },
            { name: 'Assign To Parts Specialist', color: '#10B981', bgColor: '#ECFDF5' },
            { name: 'Completed', color: '#059669', bgColor: '#D1FAE5' },
            { name: 'Hold', color: '#EF4444', bgColor: '#FEF2F2' },
            { name: 'Pending For Quote Approval', color: '#F97316', bgColor: '#FFF7ED' },
            { name: 'Rejected Warranty Claims', color: '#DC2626', bgColor: '#FEE2E2' }
        ];

        // DOM Elements
        const myQueueElement = document.getElementById('myQueueCount');
        const allQueueElement = document.getElementById('allQueueCount');
        const statusGridElement = document.getElementById('statusGrid');
        const totalWorkordersElement = document.getElementById('totalWorkorders');
        const activeStatusesElement = document.getElementById('activeStatuses');
        const updateTimeElement = document.getElementById('updateTime');

        // Initialize Dashboard
        function initializeDashboard() {
            updateQueueCounters();
            generateStatusLegend();
            updateLastUpdatedTime();

            // Set up real-time updates
            setInterval(updateDashboard, 30000); // Update every 30 seconds
            setInterval(updateLastUpdatedTime, 1000); // Update time every second
        }

        // Update Queue Counters
        function updateQueueCounters() {
            myQueueElement.textContent = dashboardData.myQueue;
            allQueueElement.textContent = dashboardData.allQueue;
        }

        // Generate Status Legend
        function generateStatusLegend() {
            const totalCount = Object.values(dashboardData.statusCounts).reduce((sum, count) => sum + count, 0);

            statusGridElement.innerHTML = '';

            statusConfig.forEach((status, index) => {
                const count = dashboardData.statusCounts[status.name] || 0;
                const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : 0;

                const statusItem = document.createElement('div');
                statusItem.className = 'status-item';
                statusItem.style.setProperty('--status-color', status.color);
                statusItem.style.setProperty('--status-bg', status.bgColor);

                statusItem.innerHTML = `
                    <div class="status-indicator"></div>
                    <div class="status-info">
                        <div class="status-name">${status.name}</div>
                        <div class="status-metrics">
                            <span class="status-count">${count}</span>
                            <span class="status-percentage">(${percentage}%)</span>
                        </div>
                    </div>
                    <div class="status-progress">
                        <div class="progress-bar" style="width: ${percentage}%"></div>
                    </div>
                `;

                statusGridElement.appendChild(statusItem);
            });

            // Update summary
            totalWorkordersElement.textContent = totalCount;
            activeStatusesElement.textContent = statusConfig.length;
        }

        // Update Last Updated Time
        function updateLastUpdatedTime() {
            const now = new Date();
            updateTimeElement.textContent = now.toLocaleTimeString();
        }

        // Simulate Real-time Updates
        function updateDashboard() {
            // Simulate queue changes
            dashboardData.myQueue += Math.floor(Math.random() * 3) - 1;
            dashboardData.allQueue += Math.floor(Math.random() * 5) - 2;

            // Ensure positive values
            dashboardData.myQueue = Math.max(0, dashboardData.myQueue);
            dashboardData.allQueue = Math.max(0, dashboardData.allQueue);

            // Simulate status changes
            const statusNames = Object.keys(dashboardData.statusCounts);
            statusNames.forEach(status => {
                const change = Math.floor(Math.random() * 3) - 1;
                dashboardData.statusCounts[status] = Math.max(0, dashboardData.statusCounts[status] + change);
            });

            // Update UI
            updateQueueCounters();
            generateStatusLegend();

            // Add loading animation
            document.querySelectorAll('.queue-counter').forEach(counter => {
                counter.classList.add('loading');
                setTimeout(() => counter.classList.remove('loading'), 1000);
            });
        }

        // Add Click Handlers for Interactive Elements
        function addEventListeners() {
            // Queue counter click handlers
            document.querySelectorAll('.queue-counter').forEach(counter => {
                counter.addEventListener('click', function() {
                    const title = this.querySelector('.queue-title').textContent;
                    showNotification(`Clicked on ${title}`);
                });
            });

            // Status item click handlers
            document.addEventListener('click', function(e) {
                if (e.target.closest('.status-item')) {
                    const statusName = e.target.closest('.status-item').querySelector('.status-name').textContent;
                    showNotification(`Clicked on status: ${statusName}`);
                }
            });
        }

        // Show Notification
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10B981;
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add CSS animations for notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize Dashboard when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            addEventListeners();
        });

        // Handle window resize for responsive updates
        window.addEventListener('resize', function() {
            // Trigger any responsive updates if needed
            generateStatusLegend();
        });
    </script>
</body>
</html>
