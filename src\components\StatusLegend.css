.status-legend {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 0.75rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--status-bg, #f9fafb);
  border: 1px solid var(--status-color, #e5e7eb)30;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.status-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px -2px var(--status-color, #000)20;
  border-color: var(--status-color, #e5e7eb);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--status-color, #6b7280);
  flex-shrink: 0;
  position: relative;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.status-item:hover .status-indicator::after {
  opacity: 1;
}

.status-info {
  flex: 1;
  min-width: 0;
}

.status-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.status-metrics {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-count {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--status-color, #6b7280);
}

.status-percentage {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.status-progress {
  width: 60px;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  flex-shrink: 0;
}

.progress-bar {
  height: 100%;
  background: var(--status-color, #6b7280);
  border-radius: 2px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.status-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.summary-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .status-item {
    padding: 0.75rem;
  }
  
  .status-summary {
    flex-direction: column;
    gap: 1rem;
  }
  
  .summary-item {
    flex-direction: row;
    gap: 0.5rem;
  }
}

@media (min-width: 1200px) {
  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Animation for status items */
.status-item {
  animation: slideInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.status-item:nth-child(1) { animation-delay: 0.1s; }
.status-item:nth-child(2) { animation-delay: 0.2s; }
.status-item:nth-child(3) { animation-delay: 0.3s; }
.status-item:nth-child(4) { animation-delay: 0.4s; }
.status-item:nth-child(5) { animation-delay: 0.5s; }
.status-item:nth-child(6) { animation-delay: 0.6s; }
.status-item:nth-child(7) { animation-delay: 0.7s; }
.status-item:nth-child(8) { animation-delay: 0.8s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
