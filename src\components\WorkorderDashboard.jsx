import React, { useState, useEffect } from 'react'
import QueueCounter from './QueueCounter'
import StatusLegend from './StatusLegend'
import './WorkorderDashboard.css'

const WorkorderDashboard = () => {
  // Mock data - replace with actual API calls
  const [dashboardData, setDashboardData] = useState({
    myQueue: 15,
    allQueue: 127,
    statusCounts: {
      'Created': 23,
      'Move to Tech': 18,
      'In Progress': 35,
      'Assign To Parts Specialist': 12,
      'Completed': 89,
      'Hold': 8,
      'Pending For Quote Approval': 14,
      'Rejected Warranty Claims': 6
    }
  })

  const statusConfig = [
    { name: 'Created', color: '#3B82F6', bgColor: '#EFF6FF' },
    { name: 'Move to Tech', color: '#8B5CF6', bgColor: '#F3E8FF' },
    { name: 'In Progress', color: '#F59E0B', bgColor: '#FFFBEB' },
    { name: 'Assign To Parts Specialist', color: '#10B981', bgColor: '#ECFDF5' },
    { name: 'Completed', color: '#059669', bgColor: '#D1FAE5' },
    { name: 'Hold', color: '#EF4444', bgColor: '#FEF2F2' },
    { name: 'Pending For Quote Approval', color: '#F97316', bgColor: '#FFF7ED' },
    { name: 'Rejected Warranty Claims', color: '#DC2626', bgColor: '#FEE2E2' }
  ]

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setDashboardData(prev => ({
        ...prev,
        myQueue: prev.myQueue + Math.floor(Math.random() * 3) - 1,
        allQueue: prev.allQueue + Math.floor(Math.random() * 5) - 2
      }))
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="workorder-dashboard">
      <header className="dashboard-header">
        <h1>Workorder Dashboard</h1>
        <div className="last-updated">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </header>

      <div className="dashboard-content">
        {/* Queue Counters Section */}
        <section className="queue-section">
          <h2>Queue Overview</h2>
          <div className="queue-counters">
            <QueueCounter 
              title="My Queue" 
              count={dashboardData.myQueue}
              icon="👤"
              color="#3B82F6"
            />
            <QueueCounter 
              title="All Queue" 
              count={dashboardData.allQueue}
              icon="📋"
              color="#10B981"
            />
          </div>
        </section>

        {/* Status Legend Section */}
        <section className="status-section">
          <h2>Status Overview</h2>
          <StatusLegend 
            statusData={dashboardData.statusCounts}
            statusConfig={statusConfig}
          />
        </section>
      </div>
    </div>
  )
}

export default WorkorderDashboard
