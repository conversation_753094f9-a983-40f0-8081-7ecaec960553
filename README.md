# Workorder Dashboard

A modern, responsive dashboard for managing workorder queues and status tracking built with React, HTML, and CSS.

## Features

### Queue Management
- **My Queue**: Personal workorder count with real-time updates
- **All Queue**: Total workorder count across all users
- Interactive counters with hover effects and trend indicators

### Status Tracking
The dashboard includes comprehensive status legends for all workorder states:
- **Created** - New workorders
- **Move to Tech** - Assigned to technical team
- **In Progress** - Currently being worked on
- **Assign To Parts Specialist** - Waiting for parts specialist
- **Completed** - Finished workorders
- **Hold** - Temporarily paused
- **Pending For Quote Approval** - Awaiting quote approval
- **Rejected Warranty Claims** - Warranty claims that were rejected

### Design Features
- **Minimal Space Consumption**: Optimized grid layout for maximum information density
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional design with smooth animations
- **Color-Coded Status**: Each status has its own color for quick identification
- **Progress Indicators**: Visual progress bars showing status distribution
- **Real-time Updates**: Automatic data refresh every 30 seconds
- **Interactive Elements**: Hover effects and smooth transitions

## Technology Stack
- **React 18**: Modern React with hooks for state management
- **Vite**: Fast build tool and development server
- **CSS3**: Modern CSS with Grid, Flexbox, and animations
- **HTML5**: Semantic markup for accessibility

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn package manager

### Installation
1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

### Development
Start the development server:
```bash
npm run dev
```
The dashboard will be available at `http://localhost:5173/`

### Build for Production
```bash
npm run build
```

## Project Structure
```
src/
├── components/
│   ├── WorkorderDashboard.jsx    # Main dashboard component
│   ├── WorkorderDashboard.css    # Dashboard styles
│   ├── QueueCounter.jsx          # Queue counter component
│   ├── QueueCounter.css          # Queue counter styles
│   ├── StatusLegend.jsx          # Status legend component
│   └── StatusLegend.css          # Status legend styles
├── App.jsx                       # Root application component
├── App.css                       # Application styles
├── main.jsx                      # Application entry point
└── index.css                     # Global styles
```

## Customization

### Adding New Status Types
To add new status types, update the `statusConfig` array in `WorkorderDashboard.jsx`:
```javascript
const statusConfig = [
  { name: 'New Status', color: '#FF6B6B', bgColor: '#FFE5E5' },
  // ... existing statuses
]
```

### Modifying Colors
Each status has customizable colors defined in the `statusConfig`. Update the `color` and `bgColor` properties to change the appearance.

### Data Integration
Replace the mock data in `WorkorderDashboard.jsx` with actual API calls:
```javascript
// Replace mock data with API calls
const fetchDashboardData = async () => {
  const response = await fetch('/api/dashboard');
  return response.json();
};
```

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Features
- Optimized CSS with minimal repaints
- Efficient React rendering with proper key props
- Smooth animations with CSS transforms
- Responsive images and layouts
- Minimal bundle size with Vite

## Accessibility
- Semantic HTML structure
- Keyboard navigation support
- Screen reader friendly
- High contrast color schemes
- Focus indicators

## License
This project is open source and available under the MIT License.
