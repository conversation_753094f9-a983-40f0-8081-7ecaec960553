import React from 'react'
import './StatusLegend.css'

const StatusLegend = ({ statusData, statusConfig }) => {
  const totalCount = Object.values(statusData).reduce((sum, count) => sum + count, 0)

  return (
    <div className="status-legend">
      <div className="status-grid">
        {statusConfig.map((status) => {
          const count = statusData[status.name] || 0
          const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : 0

          return (
            <div 
              key={status.name} 
              className="status-item"
              style={{ 
                '--status-color': status.color,
                '--status-bg': status.bgColor 
              }}
            >
              <div className="status-indicator"></div>
              <div className="status-info">
                <div className="status-name">{status.name}</div>
                <div className="status-metrics">
                  <span className="status-count">{count}</span>
                  <span className="status-percentage">({percentage}%)</span>
                </div>
              </div>
              <div className="status-progress">
                <div 
                  className="progress-bar"
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
            </div>
          )
        })}
      </div>
      
      <div className="status-summary">
        <div className="summary-item">
          <span className="summary-label">Total Workorders:</span>
          <span className="summary-value">{totalCount}</span>
        </div>
        <div className="summary-item">
          <span className="summary-label">Active Statuses:</span>
          <span className="summary-value">{statusConfig.length}</span>
        </div>
      </div>
    </div>
  )
}

export default StatusLegend
