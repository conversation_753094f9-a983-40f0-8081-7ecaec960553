.queue-counter {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--accent-color, #3B82F6)10, transparent);
  border: 2px solid var(--accent-color, #3B82F6)20;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.queue-counter::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--accent-color, #3B82F6);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.queue-counter:hover::before {
  transform: scaleX(1);
}

.queue-counter:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px -4px rgba(0, 0, 0, 0.15);
  border-color: var(--accent-color, #3B82F6);
}

.queue-icon {
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--accent-color, #3B82F6)15;
  border-radius: 50%;
  flex-shrink: 0;
}

.queue-info {
  flex: 1;
  min-width: 0;
}

.queue-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.queue-count {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-color, #3B82F6);
  line-height: 1;
  font-family: 'Segoe UI', system-ui, sans-serif;
}

.queue-trend {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.trend-indicator {
  font-size: 1.5rem;
  color: #10b981;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .queue-counter {
    padding: 1rem;
    gap: 0.75rem;
  }
  
  .queue-icon {
    width: 50px;
    height: 50px;
    font-size: 2rem;
  }
  
  .queue-count {
    font-size: 2rem;
  }
}

/* Loading state */
.queue-counter.loading {
  opacity: 0.7;
}

.queue-counter.loading .queue-count {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
