<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Work Order Management</title>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
            line-height: 1.5;
            min-height: 100vh;
        }

        .container {
            max-width: 98%;
            margin: 0 auto;
            padding: 10px;
        }

        /* Header Section */
        .header {
            background: #4a5568;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 600;
        }

        .header-icon {
            width: 24px;
            height: 24px;
            background: #48bb78;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .new-btn {
            background: #2d3748;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-btn:hover {
            background: #1a202c;
        }

        /* Main Content */
        .main-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* Sidebar and Content Layout */
        .content-layout {
            display: grid;
            grid-template-columns: 320px 1fr;
            min-height: 600px;
        }

        /* Sidebar */
        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #e2e8f0;
            padding: 16px;
        }

        .sidebar-section {
            margin-bottom: 24px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .sidebar-item:hover {
            background: #e2e8f0;
        }

        .sidebar-item.active {
            background: #2d3748;
            color: white;
        }

        .sidebar-item-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }

        .sidebar-count {
            background: #cbd5e0;
            color: #4a5568;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .sidebar-item.active .sidebar-count {
            background: #4a5568;
            color: white;
        }

        /* Legend Status Items */
        .legend-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .legend-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .legend-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .legend-text {
            flex: 1;
            font-weight: 500;
        }

        .legend-count {
            font-weight: 600;
            font-size: 11px;
        }

        /* Status Colors */
        .status-draft { background: #f7fafc; color: #4a5568; }
        .status-draft .legend-dot { background: #a0aec0; }

        .status-in-progress { background: #ebf8ff; color: #2b6cb0; }
        .status-in-progress .legend-dot { background: #3182ce; }

        .status-approved { background: #f0fff4; color: #22543d; }
        .status-approved .legend-dot { background: #38a169; }

        .status-partially-approved { background: #fffaf0; color: #c05621; }
        .status-partially-approved .legend-dot { background: #ed8936; }

        .status-rejected { background: #fed7d7; color: #c53030; }
        .status-rejected .legend-dot { background: #e53e3e; }

        .status-pending-customer { background: #e6fffa; color: #234e52; }
        .status-pending-customer .legend-dot { background: #319795; }

        .status-pending-csm { background: #faf5ff; color: #553c9a; }
        .status-pending-csm .legend-dot { background: #805ad5; }

        .status-pending-branch { background: #fff5f5; color: #742a2a; }
        .status-pending-branch .legend-dot { background: #f56565; }

        .status-pending-na { background: #fffbeb; color: #975a16; }
        .status-pending-na .legend-dot { background: #d69e2e; }

        .status-pending-vp { background: #f0f4ff; color: #3c366b; }
        .status-pending-vp .legend-dot { background: #667eea; }

        .status-pending-svp { background: #fdf2f8; color: #97266d; }
        .status-pending-svp .legend-dot { background: #d53f8c; }

        .status-pending-wd { background: #f7fafc; color: #2d3748; }
        .status-pending-wd .legend-dot { background: #4a5568; }

        /* Main Content Area */
        .content-area {
            padding: 16px;
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            gap: 16px;
        }

        .search-filters {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .search-box {
            position: relative;
            flex: 1;
            max-width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 16px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .view-toggles {
            display: flex;
            align-items: center;
            gap: 4px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 12px;
            border: none;
            background: white;
            color: #4a5568;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-btn:hover {
            background: #f7fafc;
        }

        .view-btn.active {
            background: #2d3748;
            color: white;
        }

        /* Work Order Display Container */
        .workorder-container {
            margin-top: 20px;
        }

        /* Card View (Default) */
        .workorder-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
        }

        /* List View */
        .workorder-list {
            display: none;
            flex-direction: column;
            gap: 8px;
        }

        .workorder-list.active {
            display: flex;
        }

        .workorder-list-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .workorder-list-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #cbd5e0;
        }

        .list-item-main {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }

        .list-item-id {
            font-size: 14px;
            font-weight: 600;
            color: #3182ce;
            min-width: 140px;
        }

        .list-item-details {
            flex: 1;
        }

        .list-item-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 2px;
        }

        .list-item-subtitle {
            font-size: 12px;
            color: #718096;
        }

        .list-item-status {
            min-width: 120px;
            text-align: center;
        }

        .list-item-actions {
            display: flex;
            gap: 8px;
        }

        /* Grid View */
        .grid-view-container {
            display: none;
        }

        .grid-view-container.active {
            display: block;
        }

        .grid-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 16px 0;
        }

        .grid-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }

        .grid-config-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #3182ce;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .grid-config-btn:hover {
            background: #2c5aa0;
        }

        .grid-config-btn .material-icons {
            font-size: 18px;
        }

        /* Scroll hint */
        .scroll-hint {
            display: none;
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: #234e52;
            text-align: center;
        }

        .scroll-hint.show {
            display: block;
        }

        .scroll-hint .material-icons {
            font-size: 14px;
            vertical-align: middle;
            margin-right: 4px;
        }

        .grid-table-container {
            width: 100%;
            overflow-x: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .grid-table-container::-webkit-scrollbar {
            height: 8px;
        }

        .grid-table-container::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 4px;
        }

        .grid-table-container::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .grid-table-container::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* Scroll indicators */
        .grid-table-container {
            position: relative;
        }

        .grid-table-container::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .grid-table-container.scrollable::before {
            opacity: 1;
        }

        .workorder-table {
            width: 100%;
            min-width: 800px;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
        }

        .workorder-table th {
            background: #f7fafc;
            color: #4a5568;
            font-weight: 600;
            padding: 12px 16px;
            text-align: left;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e2e8f0;
        }

        .workorder-table td {
            padding: 12px 16px;
            font-size: 14px;
            border-bottom: 1px solid #f1f5f9;
            color: #2d3748;
            vertical-align: middle;
        }

        .workorder-table tbody tr:hover {
            background: #f7fafc;
        }

        .workorder-table tbody tr:last-child td {
            border-bottom: none;
        }

        .table-id {
            font-weight: 600;
            color: #3182ce;
        }

        .table-title {
            font-weight: 600;
            color: #2d3748;
        }

        .table-subtitle {
            font-size: 12px;
            color: #718096;
            margin-top: 2px;
        }

        .table-actions {
            display: flex;
            gap: 8px;
        }

        /* Grid Configuration Modal */
        .grid-config-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        /* Edit Configuration Modal */
        .edit-config-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1001;
        }

        .edit-config-modal {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .edit-config-header {
            background: #f8f9fa;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .edit-config-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .edit-config-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #718096;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .edit-config-close:hover {
            background: #e2e8f0;
            color: #4a5568;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-button {
            flex: 1;
            padding: 16px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #718096;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            color: #4a5568;
            background: #edf2f7;
        }

        .tab-button.active {
            color: #3182ce;
            background: white;
            border-bottom-color: #3182ce;
        }

        /* Tab Content */
        .tab-content {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        /* Columns Tab */
        .columns-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
        }

        .columns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;
        }

        .column-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .column-item:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .column-checkbox {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        .column-label {
            font-size: 14px;
            color: #4a5568;
            cursor: pointer;
            flex: 1;
        }

        /* Appearance Tab */
        .appearance-section {
            margin-bottom: 24px;
        }

        .appearance-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            margin-bottom: 12px;
        }

        .appearance-option-info {
            flex: 1;
        }

        .appearance-option-title {
            font-size: 14px;
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .appearance-option-description {
            font-size: 12px;
            color: #718096;
        }

        .appearance-control {
            margin-left: 16px;
        }

        .appearance-select {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .appearance-toggle {
            position: relative;
            width: 44px;
            height: 24px;
            background: #cbd5e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .appearance-toggle.active {
            background: #3182ce;
        }

        .appearance-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
        }

        .appearance-toggle.active::after {
            transform: translateX(20px);
        }

        /* Appearance Sub-tabs */
        .appearance-sub-tabs {
            display: flex;
            margin-bottom: 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .appearance-sub-tab {
            padding: 12px 24px;
            background: none;
            border: none;
            font-size: 14px;
            color: #718096;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .appearance-sub-tab:hover {
            color: #4a5568;
        }

        .appearance-sub-tab.active {
            color: #3182ce;
            border-bottom-color: #3182ce;
        }

        /* Slider Controls */
        .slider-control {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .slider-label {
            font-size: 14px;
            color: #4a5568;
            min-width: 80px;
        }

        .slider-container {
            flex: 1;
            position: relative;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3182ce;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3182ce;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .slider-value {
            font-size: 14px;
            color: #4a5568;
            min-width: 40px;
            text-align: right;
        }

        /* Grid Preview */
        .grid-preview {
            margin-top: 24px;
        }

        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
            font-size: 12px;
        }

        .preview-table th {
            background: #f7fafc;
            color: #4a5568;
            font-weight: 600;
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .preview-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #f1f5f9;
            color: #2d3748;
        }

        .preview-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Dynamic preview styles */
        .preview-table.row-compact td,
        .preview-table.row-compact th {
            padding: 4px 8px;
        }

        .preview-table.row-comfortable td,
        .preview-table.row-comfortable th {
            padding: 12px 16px;
        }

        .preview-table.font-small {
            font-size: 10px;
        }

        .preview-table.font-large {
            font-size: 14px;
        }

        .preview-table.no-borders td,
        .preview-table.no-borders th {
            border: none;
        }

        .preview-table.striped tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .preview-table.horizontal-only td {
            border-left: none;
            border-right: none;
        }

        .preview-table.horizontal-only th {
            border-left: none;
            border-right: none;
        }

        .preview-table.vertical-only td,
        .preview-table.vertical-only th {
            border-top: none;
            border-bottom: none;
        }

        .preview-table.vertical-only td {
            border-left: 1px solid #e2e8f0;
        }

        .preview-table.vertical-only th {
            border-left: 1px solid #e2e8f0;
        }

        /* Edit Modal Footer */
        .edit-config-footer {
            background: #f8f9fa;
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .edit-footer-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
            color: #4a5568;
        }

        .edit-footer-btn:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
        }

        .edit-footer-btn.primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
        }

        .edit-footer-btn.primary:hover {
            background: #2c5aa0;
            border-color: #2c5aa0;
        }

        .grid-config-modal {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .grid-config-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .grid-config-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
        }

        .grid-config-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #718096;
            cursor: pointer;
            padding: 4px;
        }

        .grid-config-content {
            padding: 24px;
        }

        .grid-config-description {
            color: #718096;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .config-section {
            margin-bottom: 32px;
        }

        .config-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
        }

        .config-item {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px 20px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
        }

        .config-item:hover {
            border-color: #cbd5e0;
            background: #edf2f7;
        }

        .config-item.active {
            border-color: #3182ce;
            background: #ebf8ff;
        }

        .config-item-main {
            flex: 1;
        }

        .config-item-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .config-item-description {
            font-size: 12px;
            color: #718096;
        }

        .config-item-actions {
            display: flex;
            gap: 8px;
        }

        .config-action-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #4a5568;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .config-action-btn:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
        }

        .config-action-btn.primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
        }

        .config-action-btn.primary:hover {
            background: #2c5aa0;
        }

        .config-action-btn.danger {
            color: #e53e3e;
            border-color: #fed7d7;
        }

        .config-action-btn.danger:hover {
            background: #fed7d7;
        }

        .add-config-btn {
            background: #3182ce;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }

        .add-config-btn:hover {
            background: #2c5aa0;
        }

        /* Column Configuration */
        .column-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .column-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }

        .column-checkbox {
            margin: 0;
        }

        .grid-config-footer {
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: #f7fafc;
        }

        .footer-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #4a5568;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .footer-btn:hover {
            background: #f7fafc;
        }

        .footer-btn.primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
        }

        .footer-btn.primary:hover {
            background: #2c5aa0;
        }

        .workorder-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .workorder-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .card-header {
            margin-bottom: 12px;
        }

        .card-id {
            font-size: 14px;
            font-weight: 600;
            color: #3182ce;
            margin-bottom: 4px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .card-subtitle {
            font-size: 12px;
            color: #718096;
        }

        .card-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 8px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .card-action-btn {
            padding: 4px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #4a5568;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .card-action-btn:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
        }

        /* Pagination */
        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-top: 24px;
            padding: 16px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #4a5568;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background: #f7fafc;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            font-size: 14px;
            color: #718096;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-title">
                <div class="header-icon">📋</div>
                Work Order Management
            </div>
            <button class="new-btn">
                <span>+</span>
                New Work Order
            </button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-layout">
                <!-- Sidebar -->
                <div class="sidebar">
                    <!-- Queue Management -->
                    <div class="sidebar-section">
                        <div class="sidebar-title">
                            <span class="material-icons" style="font-size: 16px;">queue</span>
                            Queue Management
                        </div>
                        <div class="sidebar-item">
                            <div style="display: flex; align-items: center;">
                                <span class="material-icons sidebar-item-icon" style="font-size: 16px;">view_list</span>
                                All Queue
                            </div>
                            <span class="sidebar-count">48</span>
                        </div>
                        <div class="sidebar-item">
                            <div style="display: flex; align-items: center;">
                                <span class="material-icons sidebar-item-icon" style="font-size: 16px;">group</span>
                                Group Queue
                            </div>
                            <span class="sidebar-count">8</span>
                        </div>
                        <div class="sidebar-item active">
                            <div style="display: flex; align-items: center;">
                                <span class="material-icons sidebar-item-icon" style="font-size: 16px;">person</span>
                                My Queue
                            </div>
                            <span class="sidebar-count">6</span>
                        </div>
                    </div>

                    <!-- Legend Status -->
                    <div class="sidebar-section">
                        <div class="sidebar-title">
                            <span class="material-icons" style="font-size: 16px;">info</span>
                            Legend Status
                        </div>
                        <div class="legend-grid">
                            <div class="legend-item status-draft">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Created</span>
                                </div>
                                <span class="legend-count">8</span>
                            </div>
                            <div class="legend-item status-in-progress">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Movedto Tech</span>
                                </div>
                                <span class="legend-count">6</span>
                            </div>
                            <div class="legend-item status-approved">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">In Progress</span>
                                </div>
                                <span class="legend-count">8</span>
                            </div>
                            <div class="legend-item status-partially-approved">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Job Completed by Tech</span>
                                </div>
                                <span class="legend-count">3</span>
                            </div>
                            <div class="legend-item status-rejected">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Completed</span>
                                </div>
                                <span class="legend-count">1</span>
                            </div>
                            <div class="legend-item status-pending-customer">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Assign Task Parts Specialist</span>
                                </div>
                                <span class="legend-count">5</span>
                            </div>
                            <div class="legend-item status-pending-csm">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Hold</span>
                                </div>
                                <span class="legend-count">8</span>
                            </div>
                            <div class="legend-item status-pending-branch">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Closed</span>
                                </div>
                                <span class="legend-count">3</span>
                            </div>
                            <div class="legend-item status-pending-vp">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Pending for Quote Approval</span>
                                </div>
                                <span class="legend-count">8</span>
                            </div>
                            <div class="legend-item status-pending-svp">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Rejected Warranty Claims</span>
                                </div>
                                <span class="legend-count">3</span>
                            </div>
                            <!-- <div class="legend-item status-pending-na">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Pending for National Service D...</span>
                                </div>
                                <span class="legend-count">5</span>
                            </div>
                            <div class="legend-item status-pending-wd">
                                <div style="display: flex; align-items: center;">
                                    <div class="legend-dot"></div>
                                    <span class="legend-text">Pending for WD Creation</span>
                                </div>
                                <span class="legend-count">1</span>
                            </div> -->
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="content-area">
                    <!-- Content Header -->
                    <div class="content-header">
                        <div class="search-filters">
                            <div class="search-box">
                                <span class="material-icons search-icon">search</span>
                                <input type="text" class="search-input" placeholder="Search work orders...">
                            </div>
                            <select class="filter-select">
                                <option>All Status</option>
                                <option>Draft</option>
                                <option>In Progress</option>
                                <option>Approved</option>
                                <option>Completed</option>
                            </select>
                            <select class="filter-select">
                                <option>All Customers</option>
                                <option>ABC Manufacturing</option>
                                <option>XYZ Logistics</option>
                                <option>Tech Solutions</option>
                            </select>
                        </div>
                        <div class="view-toggles">
                            <button class="view-btn">📋 List</button>
                            <button class="view-btn active">📄 Card</button>
                            <button class="view-btn">📊 Grid</button>
                        </div>
                    </div>

                    <!-- Work Order Display Container -->
                    <div class="workorder-container">
                        <!-- Card View -->
                        <div class="workorder-grid" id="cardView">
                            <!-- Cards will be generated here -->
                        </div>

                        <!-- List View -->
                        <div class="workorder-list" id="listView">
                            <!-- List items will be generated here -->
                        </div>

                        <!-- Grid/Table View -->
                        <div class="grid-view-container" id="gridView">
                            <div class="grid-header">
                                <div class="grid-title">Work Orders Grid</div>
                                <button class="grid-config-btn" onclick="showGridConfigModal()" title="Grid Configurations">
                                    <span class="material-icons">settings</span>
                                    Grid Configurations
                                </button>
                            </div>
                            <div class="scroll-hint" id="scrollHint">
                                <span class="material-icons">swipe_left</span>
                                Scroll horizontally to view all columns
                            </div>
                            <div class="grid-table-container">
                                <table class="workorder-table" id="gridTable">
                                    <thead>
                                        <tr>
                                            <th>Work Order ID</th>
                                            <th>Customer Details</th>
                                            <th>Vehicle Info</th>
                                            <th>Status</th>
                                            <th>Dates</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="gridViewBody">
                                        <!-- Table rows will be generated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination">
                        <button class="pagination-btn" id="prevBtn">← Previous</button>
                        <span class="pagination-info">Page 1 of 1</span>
                        <button class="pagination-btn" id="nextBtn">Next →</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grid Configuration Modal -->
    <div class="grid-config-overlay" id="gridConfigModal">
        <div class="grid-config-modal">
            <div class="grid-config-header">
                <h2 class="grid-config-title">Grid Configurations</h2>
                <button class="grid-config-close" onclick="closeGridConfig()">&times;</button>
            </div>

            <div class="grid-config-content">
                <p class="grid-config-description">
                    Manage your saved grid configurations or create a new one.
                </p>

                <div class="config-section">
                    <button class="add-config-btn" onclick="addNewConfiguration()">
                        <span class="material-icons" style="font-size: 16px;">add</span>
                        ADD CONFIGURATION
                    </button>
                </div>

                <div class="config-section">
                    <div id="configurationsList">
                        <!-- Default View -->
                        <div class="config-item active" data-config="default">
                            <div class="config-item-main">
                                <div class="config-item-title">Default View</div>
                                <div class="config-item-description">5 visible columns</div>
                            </div>
                            <div class="config-item-actions">
                                <button class="config-action-btn primary" onclick="applyConfiguration('default')">Apply</button>
                                <button class="config-action-btn" onclick="editConfiguration('default')">✏️</button>
                                <button class="config-action-btn danger" onclick="deleteConfiguration('default')">🗑️</button>
                            </div>
                        </div>

                        <!-- Compact View -->
                        <div class="config-item" data-config="compact">
                            <div class="config-item-main">
                                <div class="config-item-title">Compact View</div>
                                <div class="config-item-description">5 visible columns</div>
                            </div>
                            <div class="config-item-actions">
                                <button class="config-action-btn primary" onclick="applyConfiguration('compact')">Apply</button>
                                <button class="config-action-btn" onclick="editConfiguration('compact')">✏️</button>
                                <button class="config-action-btn danger" onclick="deleteConfiguration('compact')">🗑️</button>
                            </div>
                        </div>

                        <!-- Detailed View -->
                        <div class="config-item" data-config="detailed">
                            <div class="config-item-main">
                                <div class="config-item-title">Detailed View</div>
                                <div class="config-item-description">15 visible columns</div>
                            </div>
                            <div class="config-item-actions">
                                <button class="config-action-btn primary" onclick="applyConfiguration('detailed')">Apply</button>
                                <button class="config-action-btn" onclick="editConfiguration('detailed')">✏️</button>
                                <button class="config-action-btn danger" onclick="deleteConfiguration('detailed')">🗑️</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Column Configuration Section (Hidden by default) -->
                <div class="config-section" id="columnConfigSection" style="display: none;">
                    <div class="config-section-title">Column Configuration</div>
                    <div class="column-config" id="columnConfig">
                        <!-- Column checkboxes will be generated here -->
                    </div>
                </div>
            </div>

            <div class="grid-config-footer">
                <button class="footer-btn" onclick="closeGridConfig()">Cancel</button>
                <button class="footer-btn primary" onclick="saveConfiguration()">Apply</button>
            </div>
        </div>
    </div>

    <!-- Edit Configuration Modal -->
    <div class="edit-config-overlay" id="editConfigModal">
        <div class="edit-config-modal">
            <div class="edit-config-header">
                <h2 class="edit-config-title" id="editConfigTitle">Edit Grid Configuration</h2>
                <button class="edit-config-close" onclick="closeEditConfig()">&times;</button>
            </div>

            <div class="tab-navigation">
                <button class="tab-button active" onclick="switchTab('columns')">Columns</button>
                <button class="tab-button" onclick="switchTab('appearance')">Appearance</button>
            </div>

            <div class="tab-content">
                <!-- Columns Tab -->
                <div class="tab-panel active" id="columnsTab">
                    <div class="columns-section">
                        <div class="section-title">Select Columns to Display</div>
                        <div class="columns-grid" id="editColumnsGrid">
                            <!-- Column checkboxes will be generated here -->
                        </div>
                    </div>
                </div>

                <!-- Appearance Tab -->
                <div class="tab-panel" id="appearanceTab">
                    <!-- Sub-tabs for Basic/Advanced -->
                    <div class="appearance-sub-tabs">
                        <button class="appearance-sub-tab active" onclick="switchAppearanceTab('basic')">Basic</button>
                        <button class="appearance-sub-tab" onclick="switchAppearanceTab('advanced')">Advanced</button>
                    </div>

                    <!-- Basic Appearance Settings -->
                    <div class="appearance-section" id="basicAppearance">
                        <!-- Row Height Slider -->
                        <div class="slider-control">
                            <div class="slider-label">Row Height</div>
                            <div class="slider-container">
                                <input type="range" class="slider" id="rowHeightSlider" min="24" max="48" value="32"
                                       oninput="updateSliderValue('rowHeight', this.value)">
                            </div>
                            <div class="slider-value" id="rowHeightValue">32px</div>
                        </div>

                        <!-- Font Size Slider -->
                        <div class="slider-control">
                            <div class="slider-label">Font Size</div>
                            <div class="slider-container">
                                <input type="range" class="slider" id="fontSizeSlider" min="10" max="16" value="12"
                                       oninput="updateSliderValue('fontSize', this.value)">
                            </div>
                            <div class="slider-value" id="fontSizeValue">12px</div>
                        </div>

                        <!-- Grid Lines Dropdown -->
                        <div class="appearance-option">
                            <div class="appearance-option-info">
                                <div class="appearance-option-title">Grid Lines</div>
                            </div>
                            <div class="appearance-control">
                                <select class="appearance-select" id="gridLines" onchange="updateGridPreview()">
                                    <option value="all">All Lines</option>
                                    <option value="horizontal">Horizontal Lines Only</option>
                                    <option value="vertical">Vertical Lines Only</option>
                                    <option value="none">No Lines</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Appearance Settings -->
                    <div class="appearance-section" id="advancedAppearance" style="display: none;">
                        <div class="appearance-option">
                            <div class="appearance-option-info">
                                <div class="appearance-option-title">Striped Rows</div>
                                <div class="appearance-option-description">Alternate row background colors</div>
                            </div>
                            <div class="appearance-control">
                                <div class="appearance-toggle" id="stripedRows" onclick="toggleAppearance('stripedRows')"></div>
                            </div>
                        </div>

                        <div class="appearance-option">
                            <div class="appearance-option-info">
                                <div class="appearance-option-title">Hover Effects</div>
                                <div class="appearance-option-description">Highlight rows on hover</div>
                            </div>
                            <div class="appearance-control">
                                <div class="appearance-toggle active" id="hoverEffects" onclick="toggleAppearance('hoverEffects')"></div>
                            </div>
                        </div>

                        <div class="appearance-option">
                            <div class="appearance-option-info">
                                <div class="appearance-option-title">Dense Layout</div>
                                <div class="appearance-option-description">Reduce spacing for more data</div>
                            </div>
                            <div class="appearance-control">
                                <div class="appearance-toggle" id="denseLayout" onclick="toggleAppearance('denseLayout')"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Grid Preview -->
                    <div class="grid-preview">
                        <div class="preview-title">Grid Preview</div>
                        <table class="preview-table" id="previewTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Product Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Product 1</td>
                                    <td>Electronics</td>
                                    <td>$99.99</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Product 2</td>
                                    <td>Clothing</td>
                                    <td>$199.98</td>
                                    <td>20</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Product 3</td>
                                    <td>Home & Kitchen</td>
                                    <td>$299.97</td>
                                    <td>30</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="edit-config-footer">
                <button class="edit-footer-btn" onclick="closeEditConfig()">Cancel</button>
                <button class="edit-footer-btn primary" onclick="saveEditConfiguration()">Save Changes</button>
            </div>
        </div>
    </div>

    <script>
        // Sample Work Order Data
        const workOrders = [
            {
                id: 'WO/17/01/153/2023-7',
                title: 'SUBURBAN SCHOOL DISTRICT',
                subtitle: 'Blue Bird Vision',
                status: 'COMPLETED',
                statusClass: 'status-approved',
                customerAccount: 'CUST-12345',
                customerName: 'Suburban School District',
                vinNumber: '1HGBH41JXMN109186',
                chassisNumber: 'CH-789456',
                quotationNumber: 'QT-2024-001',
                ticketNumber: 'TK-445566',
                plannedStartDate: '2024-01-15',
                plannedCompletionDate: '2024-01-20',
                unitNumber: 'UNIT-001',
                doorCode: 'DC-A1B2',
                isDropIn: 'Yes',
                keyTag: 'KEY-789',
                bay: 'Bay-A1',
                priority: 'High'
            },
            {
                id: 'WO/17/01/154/2023-8',
                title: 'CONSTRUCTION FLEET MANAGEMENT',
                subtitle: 'Caterpillar 336',
                status: 'ON-HOLD',
                statusClass: 'status-rejected',
                customerAccount: 'CUST-67890',
                customerName: 'Construction Fleet Management',
                vinNumber: '2T1BURHE0JC123456',
                chassisNumber: 'CH-123789',
                quotationNumber: 'QT-2024-002',
                ticketNumber: 'TK-778899',
                plannedStartDate: '2024-01-16',
                plannedCompletionDate: '2024-01-22',
                unitNumber: 'UNIT-002',
                doorCode: 'DC-C3D4',
                isDropIn: 'No',
                keyTag: 'KEY-456',
                bay: 'Bay-B2',
                priority: 'Medium'
            },
            {
                id: 'WO/17/01/155/2023-9',
                title: 'EMERGENCY MEDICAL SERVICES',
                subtitle: 'Ford E-450',
                status: 'APPROVED',
                statusClass: 'status-approved',
                customerAccount: 'CUST-11111',
                customerName: 'Emergency Medical Services',
                vinNumber: '3VWDP7AJ5DM123789',
                chassisNumber: 'CH-456123',
                quotationNumber: 'QT-2024-003',
                ticketNumber: 'TK-112233',
                plannedStartDate: '2024-01-17',
                plannedCompletionDate: '2024-01-25',
                unitNumber: 'UNIT-003',
                doorCode: 'DC-E5F6',
                isDropIn: 'Yes',
                keyTag: 'KEY-123',
                bay: 'Bay-C3',
                priority: 'Low'
            },
            {
                id: 'WO/17/01/156/2023-10',
                title: 'WASTE MANAGEMENT SOLUTIONS',
                subtitle: 'Mack TerraPro',
                status: 'COMPLETED',
                statusClass: 'status-approved',
                customerAccount: 'CUST-22222',
                customerName: 'Waste Management Solutions',
                vinNumber: '4T1BF1FK5DU789456',
                chassisNumber: 'CH-789123',
                quotationNumber: 'QT-2024-004',
                ticketNumber: 'TK-445577',
                plannedStartDate: '2024-01-18',
                plannedCompletionDate: '2024-01-24',
                unitNumber: 'UNIT-004',
                doorCode: 'DC-G7H8',
                isDropIn: 'No',
                keyTag: 'KEY-987',
                bay: 'Bay-D4',
                priority: 'High'
            },
            {
                id: 'WO/17/01/157/2023-11',
                title: 'METRO TRANSPORT SERVICES',
                subtitle: 'Ford Transit 350',
                status: 'IN PROGRESS',
                statusClass: 'status-in-progress',
                customerAccount: 'CUST-33333',
                customerName: 'Metro Transport Services',
                vinNumber: '5NPE34AF5FH456789',
                chassisNumber: 'CH-321654',
                quotationNumber: 'QT-2024-005',
                ticketNumber: 'TK-998877',
                plannedStartDate: '2024-01-19',
                plannedCompletionDate: '2024-01-26',
                unitNumber: 'UNIT-005',
                doorCode: 'DC-I9J0',
                isDropIn: 'Yes',
                keyTag: 'KEY-654',
                bay: 'Bay-E5',
                priority: 'Medium'
            },
            {
                id: 'WO/17/01/158/2023-12',
                title: 'RAPID DELIVERY CORP',
                subtitle: 'Mercedes Sprinter',
                status: 'DRAFT',
                statusClass: 'status-draft',
                customerAccount: 'CUST-44444',
                customerName: 'Rapid Delivery Corp',
                vinNumber: '1FTFW1ET5DFC12345',
                chassisNumber: 'CH-987321',
                quotationNumber: 'QT-2024-006',
                ticketNumber: 'TK-556677',
                plannedStartDate: '2024-01-20',
                plannedCompletionDate: '2024-01-28',
                unitNumber: 'UNIT-006',
                doorCode: 'DC-K1L2',
                isDropIn: 'No',
                keyTag: 'KEY-321',
                bay: 'Bay-F6',
                priority: 'High'
            }
        ];

        // Current view mode
        let currentView = 'card';

        // Grid configurations
        let gridConfigurations = {
            default: {
                name: 'Default View',
                columns: ['id', 'customer', 'vehicle', 'status', 'dates'],
                description: '5 visible columns'
            },
            compact: {
                name: 'Compact View',
                columns: ['id', 'customer', 'status', 'dates', 'actions'],
                description: '5 visible columns'
            },
            detailed: {
                name: 'Detailed View',
                columns: ['id', 'customer', 'vehicle', 'status', 'dates', 'customerAccount', 'vinNumber', 'chassisNumber', 'quotationNumber', 'ticketNumber', 'unitNumber', 'doorCode', 'isDropIn', 'keyTag', 'bay'],
                description: '15 visible columns'
            }
        };

        let currentGridConfig = 'default';

        // Available columns for configuration
        const availableColumns = {
            id: 'Work Order ID',
            customer: 'Customer Details',
            vehicle: 'Vehicle Info',
            status: 'Status',
            dates: 'Dates',
            customerAccount: 'Customer Account',
            vinNumber: 'VIN Number',
            chassisNumber: 'Chassis Number',
            quotationNumber: 'Quotation Number',
            ticketNumber: 'Ticket Number',
            unitNumber: 'Unit Number',
            doorCode: 'Door Code',
            isDropIn: 'Is Drop In',
            keyTag: 'Key Tag',
            bay: 'Bay',
            actions: 'Actions'
        };

        // Initialize the application
        function initializeApp() {
            renderWorkOrders();
            setupEventListeners();
        }

        // Render work orders in all views
        function renderWorkOrders() {
            renderCardView();
            renderListView();
            renderGridView();
        }

        // Render Card View
        function renderCardView() {
            const cardContainer = document.getElementById('cardView');
            cardContainer.innerHTML = '';

            workOrders.forEach(workOrder => {
                const card = createWorkOrderCard(workOrder);
                cardContainer.appendChild(card);
            });
        }

        // Render List View
        function renderListView() {
            const listContainer = document.getElementById('listView');
            listContainer.innerHTML = '';

            workOrders.forEach(workOrder => {
                const listItem = createWorkOrderListItem(workOrder);
                listContainer.appendChild(listItem);
            });
        }

        // Render Grid/Table View
        function renderGridView() {
            const gridBody = document.getElementById('gridViewBody');
            gridBody.innerHTML = '';

            workOrders.forEach(workOrder => {
                const row = createWorkOrderTableRow(workOrder);
                gridBody.appendChild(row);
            });
        }

        // Create individual work order card
        function createWorkOrderCard(workOrder) {
            const card = document.createElement('div');
            card.className = 'workorder-card';

            card.innerHTML = `
                <div class="card-header">
                    <div class="card-id">${workOrder.id}</div>
                    <div class="card-title">${workOrder.title}</div>
                    <div class="card-subtitle">${workOrder.subtitle}</div>
                    <div class="card-status ${workOrder.statusClass}">${workOrder.status}</div>
                </div>
                <div class="card-actions">
                    <button class="card-action-btn" onclick="editWorkOrder('${workOrder.id}')">✏️</button>
                    <button class="card-action-btn" onclick="deleteWorkOrder('${workOrder.id}')">🗑️</button>
                </div>
            `;

            return card;
        }

        // Create work order list item
        function createWorkOrderListItem(workOrder) {
            const listItem = document.createElement('div');
            listItem.className = 'workorder-list-item';

            listItem.innerHTML = `
                <div class="list-item-main">
                    <div class="list-item-id">${workOrder.id}</div>
                    <div class="list-item-details">
                        <div class="list-item-title">${workOrder.title}</div>
                        <div class="list-item-subtitle">${workOrder.subtitle}</div>
                    </div>
                    <div class="list-item-status">
                        <div class="card-status ${workOrder.statusClass}">${workOrder.status}</div>
                    </div>
                </div>
                <div class="list-item-actions">
                    <button class="card-action-btn" onclick="editWorkOrder('${workOrder.id}')">✏️</button>
                    <button class="card-action-btn" onclick="deleteWorkOrder('${workOrder.id}')">🗑️</button>
                </div>
            `;

            return listItem;
        }

        // Create work order table row
        function createWorkOrderTableRow(workOrder) {
            const row = document.createElement('tr');

            row.innerHTML = `
                <td>
                    <div class="table-id">${workOrder.id}</div>
                </td>
                <td>
                    <div class="table-title">${workOrder.title}</div>
                    <div class="table-subtitle">Account: ${workOrder.customerAccount}</div>
                </td>
                <td>
                    <div class="table-title">${workOrder.subtitle}</div>
                    <div class="table-subtitle">VIN: ${workOrder.vinNumber}</div>
                </td>
                <td>
                    <div class="card-status ${workOrder.statusClass}">${workOrder.status}</div>
                </td>
                <td>
                    <div style="font-size: 12px;">
                        <div><strong>Start:</strong> ${formatDate(workOrder.plannedStartDate)}</div>
                        <div><strong>End:</strong> ${formatDate(workOrder.plannedCompletionDate)}</div>
                    </div>
                </td>
                <td>
                    <div class="table-actions">
                        <button class="card-action-btn" onclick="editWorkOrder('${workOrder.id}')">✏️</button>
                        <button class="card-action-btn" onclick="deleteWorkOrder('${workOrder.id}')">🗑️</button>
                    </div>
                </td>
            `;

            return row;
        }

        // Format date helper function
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        }

        // Setup event listeners
        function setupEventListeners() {
            // Search functionality
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', handleSearch);

            // Filter functionality
            const filterSelects = document.querySelectorAll('.filter-select');
            filterSelects.forEach(select => {
                select.addEventListener('change', handleFilter);
            });

            // View toggle functionality
            const viewBtns = document.querySelectorAll('.view-btn');
            viewBtns.forEach(btn => {
                btn.addEventListener('click', handleViewToggle);
            });

            // Sidebar item clicks
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.addEventListener('click', handleSidebarClick);
            });

            // Legend item clicks
            const legendItems = document.querySelectorAll('.legend-item');
            legendItems.forEach(item => {
                item.addEventListener('click', handleLegendClick);
            });
        }

        // Handle search
        function handleSearch(event) {
            const searchTerm = event.target.value.toLowerCase();
            console.log('Searching for:', searchTerm);
            // Implement search logic here
        }

        // Handle filter
        function handleFilter(event) {
            const filterValue = event.target.value;
            console.log('Filtering by:', filterValue);
            // Implement filter logic here
        }

        // Handle view toggle
        function handleViewToggle(event) {
            // Remove active class from all buttons
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            event.target.classList.add('active');

            const viewText = event.target.textContent.trim();

            // Hide all views
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('listView').classList.remove('active');
            document.getElementById('gridView').classList.remove('active');

            // Show selected view
            if (viewText.includes('List')) {
                currentView = 'list';
                document.getElementById('listView').classList.add('active');
                console.log('Switched to List view');
            } else if (viewText.includes('Card')) {
                currentView = 'card';
                document.getElementById('cardView').style.display = 'grid';
                console.log('Switched to Card view');
            } else if (viewText.includes('Grid')) {
                currentView = 'grid';
                document.getElementById('gridView').classList.add('active');
                renderGridViewWithConfig(currentGridConfig);
                console.log('Switched to Grid view');
            }
        }

        // Handle sidebar clicks
        function handleSidebarClick(event) {
            // Remove active class from all sidebar items
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to clicked item
            event.currentTarget.classList.add('active');

            console.log('Sidebar item clicked');
        }

        // Handle legend clicks
        function handleLegendClick(event) {
            const legendItem = event.currentTarget;
            const statusClass = legendItem.className.split(' ').find(cls => cls.startsWith('status-'));
            console.log('Legend item clicked:', statusClass);
            // Implement filtering by status here
        }

        // Work order actions
        function editWorkOrder(id) {
            console.log('Editing work order:', id);
            alert(`Editing work order: ${id}`);
        }

        function deleteWorkOrder(id) {
            console.log('Deleting work order:', id);
            if (confirm(`Are you sure you want to delete work order: ${id}?`)) {
                // Remove from array and re-render
                const index = workOrders.findIndex(wo => wo.id === id);
                if (index > -1) {
                    workOrders.splice(index, 1);
                    renderWorkOrders();
                }
            }
        }

        // Grid Configuration Functions
        function showGridConfigModal() {
            document.getElementById('gridConfigModal').style.display = 'flex';
            updateConfigurationsList();
        }

        function closeGridConfig() {
            document.getElementById('gridConfigModal').style.display = 'none';
            document.getElementById('columnConfigSection').style.display = 'none';
        }

        function updateConfigurationsList() {
            const configsList = document.getElementById('configurationsList');
            configsList.innerHTML = '';

            Object.keys(gridConfigurations).forEach(configKey => {
                const config = gridConfigurations[configKey];
                const configItem = document.createElement('div');
                configItem.className = `config-item ${configKey === currentGridConfig ? 'active' : ''}`;
                configItem.setAttribute('data-config', configKey);

                configItem.innerHTML = `
                    <div class="config-item-main">
                        <div class="config-item-title">${config.name}</div>
                        <div class="config-item-description">${config.description}</div>
                    </div>
                    <div class="config-item-actions">
                        <button class="config-action-btn primary" onclick="applyConfiguration('${configKey}')">Apply</button>
                        <button class="config-action-btn" onclick="editConfiguration('${configKey}')">✏️</button>
                        <button class="config-action-btn danger" onclick="deleteConfiguration('${configKey}')">🗑️</button>
                    </div>
                `;

                configsList.appendChild(configItem);
            });
        }

        function applyConfiguration(configKey) {
            currentGridConfig = configKey;

            // Update active state
            document.querySelectorAll('.config-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-config="${configKey}"]`).classList.add('active');

            // Switch to grid view and apply configuration
            switchToGridView();
            renderGridViewWithConfig(configKey);
            closeGridConfig();

            console.log(`Applied configuration: ${gridConfigurations[configKey].name}`);
        }

        function switchToGridView() {
            // Update view buttons
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('.view-btn:last-child').classList.add('active');

            // Hide other views and show grid
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('listView').classList.remove('active');
            document.getElementById('gridView').classList.add('active');

            currentView = 'grid';
        }

        function renderGridViewWithConfig(configKey) {
            const config = gridConfigurations[configKey];
            const table = document.getElementById('gridTable');
            const thead = table.querySelector('thead tr');
            const tbody = document.getElementById('gridViewBody');

            // Clear existing content
            thead.innerHTML = '';
            tbody.innerHTML = '';

            // Calculate minimum width based on number of columns
            const columnCount = config.columns.length;
            const minColumnWidth = 150; // Minimum width per column
            const calculatedMinWidth = Math.max(800, columnCount * minColumnWidth);
            table.style.minWidth = `${calculatedMinWidth}px`;

            // Add headers based on configuration
            config.columns.forEach(columnKey => {
                const th = document.createElement('th');
                th.textContent = availableColumns[columnKey] || columnKey;
                thead.appendChild(th);
            });

            // Add rows based on configuration
            workOrders.forEach(workOrder => {
                const row = document.createElement('tr');

                config.columns.forEach(columnKey => {
                    const td = document.createElement('td');

                    switch(columnKey) {
                        case 'id':
                            td.innerHTML = `<div class="table-id">${workOrder.id}</div>`;
                            break;
                        case 'customer':
                            td.innerHTML = `
                                <div class="table-title">${workOrder.title}</div>
                                <div class="table-subtitle">Account: ${workOrder.customerAccount}</div>
                            `;
                            break;
                        case 'vehicle':
                            td.innerHTML = `
                                <div class="table-title">${workOrder.subtitle}</div>
                                <div class="table-subtitle">VIN: ${workOrder.vinNumber}</div>
                            `;
                            break;
                        case 'status':
                            td.innerHTML = `<div class="card-status ${workOrder.statusClass}">${workOrder.status}</div>`;
                            break;
                        case 'dates':
                            td.innerHTML = `
                                <div style="font-size: 12px;">
                                    <div><strong>Start:</strong> ${formatDate(workOrder.plannedStartDate)}</div>
                                    <div><strong>End:</strong> ${formatDate(workOrder.plannedCompletionDate)}</div>
                                </div>
                            `;
                            break;
                        case 'customerAccount':
                            td.textContent = workOrder.customerAccount;
                            break;
                        case 'vinNumber':
                            td.textContent = workOrder.vinNumber;
                            break;
                        case 'chassisNumber':
                            td.textContent = workOrder.chassisNumber;
                            break;
                        case 'quotationNumber':
                            td.textContent = workOrder.quotationNumber;
                            break;
                        case 'ticketNumber':
                            td.textContent = workOrder.ticketNumber;
                            break;
                        case 'unitNumber':
                            td.textContent = workOrder.unitNumber;
                            break;
                        case 'doorCode':
                            td.textContent = workOrder.doorCode;
                            break;
                        case 'isDropIn':
                            td.textContent = workOrder.isDropIn;
                            break;
                        case 'keyTag':
                            td.textContent = workOrder.keyTag;
                            break;
                        case 'bay':
                            td.textContent = workOrder.bay;
                            break;
                        case 'actions':
                            td.innerHTML = `
                                <div class="table-actions">
                                    <button class="card-action-btn" onclick="editWorkOrder('${workOrder.id}')">✏️</button>
                                    <button class="card-action-btn" onclick="deleteWorkOrder('${workOrder.id}')">🗑️</button>
                                </div>
                            `;
                            break;
                        default:
                            td.textContent = workOrder[columnKey] || '';
                    }

                    row.appendChild(td);
                });

                tbody.appendChild(row);
            });

            // Check if table is scrollable and add visual indicator
            setTimeout(() => {
                const container = document.querySelector('.grid-table-container');
                const scrollHint = document.getElementById('scrollHint');

                if (container.scrollWidth > container.clientWidth) {
                    container.classList.add('scrollable');
                    scrollHint.classList.add('show');
                } else {
                    container.classList.remove('scrollable');
                    scrollHint.classList.remove('show');
                }
            }, 100);
        }

        function editConfiguration(configKey) {
            // Store the configuration being edited
            window.currentEditingConfig = configKey;

            // Update modal title
            const config = gridConfigurations[configKey];
            document.getElementById('editConfigTitle').textContent = `Edit ${config.name}`;

            // Show edit configuration modal
            document.getElementById('editConfigModal').style.display = 'flex';

            // Generate columns for editing
            generateEditColumnsGrid(configKey);

            // Load appearance settings
            loadAppearanceSettings(configKey);
        }

        function generateColumnConfig(configKey) {
            const config = gridConfigurations[configKey];
            const columnConfig = document.getElementById('columnConfig');
            columnConfig.innerHTML = '';

            Object.keys(availableColumns).forEach(columnKey => {
                const columnItem = document.createElement('div');
                columnItem.className = 'column-item';

                const isChecked = config.columns.includes(columnKey);

                columnItem.innerHTML = `
                    <input type="checkbox" class="column-checkbox" id="col-${columnKey}"
                           ${isChecked ? 'checked' : ''} data-column="${columnKey}">
                    <label for="col-${columnKey}">${availableColumns[columnKey]}</label>
                `;

                columnConfig.appendChild(columnItem);
            });
        }

        function saveConfiguration() {
            const checkboxes = document.querySelectorAll('.column-checkbox');
            const selectedColumns = [];

            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedColumns.push(checkbox.getAttribute('data-column'));
                }
            });

            // Update current configuration
            if (selectedColumns.length > 0) {
                gridConfigurations[currentGridConfig].columns = selectedColumns;
                gridConfigurations[currentGridConfig].description = `${selectedColumns.length} visible columns`;

                // Apply the updated configuration
                renderGridViewWithConfig(currentGridConfig);
                updateConfigurationsList();

                console.log(`Updated configuration: ${gridConfigurations[currentGridConfig].name}`);
            }

            closeGridConfig();
        }

        function deleteConfiguration(configKey) {
            if (Object.keys(gridConfigurations).length <= 1) {
                alert('Cannot delete the last configuration.');
                return;
            }

            if (confirm(`Are you sure you want to delete "${gridConfigurations[configKey].name}"?`)) {
                delete gridConfigurations[configKey];

                // If deleted config was current, switch to first available
                if (currentGridConfig === configKey) {
                    currentGridConfig = Object.keys(gridConfigurations)[0];
                }

                updateConfigurationsList();
                console.log(`Deleted configuration: ${configKey}`);
            }
        }

        function addNewConfiguration() {
            const name = prompt('Enter configuration name:');
            if (name && name.trim()) {
                const configKey = name.toLowerCase().replace(/\s+/g, '-');
                gridConfigurations[configKey] = {
                    name: name.trim(),
                    columns: ['id', 'customer', 'vehicle', 'status', 'actions'],
                    description: '5 visible columns'
                };
                updateConfigurationsList();
                console.log(`Added new configuration: ${name}`);
            }
        }

        // Edit Configuration Modal Functions
        function closeEditConfig() {
            document.getElementById('editConfigModal').style.display = 'none';
            window.currentEditingConfig = null;
        }

        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');

            // Update tab panels
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            document.getElementById(`${tabName}Tab`).classList.add('active');
        }

        function generateEditColumnsGrid(configKey) {
            const config = gridConfigurations[configKey];
            const columnsGrid = document.getElementById('editColumnsGrid');
            columnsGrid.innerHTML = '';

            Object.keys(availableColumns).forEach(columnKey => {
                const columnItem = document.createElement('div');
                columnItem.className = 'column-item';

                const isChecked = config.columns.includes(columnKey);

                columnItem.innerHTML = `
                    <input type="checkbox" class="column-checkbox" id="edit-col-${columnKey}"
                           ${isChecked ? 'checked' : ''} data-column="${columnKey}">
                    <label for="edit-col-${columnKey}" class="column-label">${availableColumns[columnKey]}</label>
                `;

                columnsGrid.appendChild(columnItem);
            });
        }

        function loadAppearanceSettings(configKey) {
            const config = gridConfigurations[configKey];
            const appearance = config.appearance || {};

            // Load slider values
            const rowHeight = appearance.rowHeight || 32;
            const fontSize = appearance.fontSize || 12;

            document.getElementById('rowHeightSlider').value = rowHeight;
            document.getElementById('fontSizeSlider').value = fontSize;
            document.getElementById('rowHeightValue').textContent = rowHeight + 'px';
            document.getElementById('fontSizeValue').textContent = fontSize + 'px';

            // Load dropdown values
            document.getElementById('gridLines').value = appearance.gridLines || 'all';

            // Set toggle states
            setToggleState('stripedRows', appearance.stripedRows || false);
            setToggleState('hoverEffects', appearance.hoverEffects !== false);
            setToggleState('denseLayout', appearance.denseLayout || false);

            // Update preview
            updateGridPreview();
        }

        function setToggleState(toggleId, isActive) {
            const toggle = document.getElementById(toggleId);
            if (isActive) {
                toggle.classList.add('active');
            } else {
                toggle.classList.remove('active');
            }
        }

        function switchAppearanceTab(tabName) {
            // Update sub-tab buttons
            document.querySelectorAll('.appearance-sub-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[onclick="switchAppearanceTab('${tabName}')"]`).classList.add('active');

            // Show/hide sections
            if (tabName === 'basic') {
                document.getElementById('basicAppearance').style.display = 'block';
                document.getElementById('advancedAppearance').style.display = 'none';
            } else {
                document.getElementById('basicAppearance').style.display = 'none';
                document.getElementById('advancedAppearance').style.display = 'block';
            }
        }

        function updateSliderValue(type, value) {
            document.getElementById(type + 'Value').textContent = value + 'px';
            updateGridPreview();
        }

        function updateGridPreview() {
            const previewTable = document.getElementById('previewTable');
            const rowHeight = document.getElementById('rowHeightSlider').value;
            const fontSize = document.getElementById('fontSizeSlider').value;
            const gridLines = document.getElementById('gridLines').value;
            const stripedRows = document.getElementById('stripedRows').classList.contains('active');
            const hoverEffects = document.getElementById('hoverEffects').classList.contains('active');
            const denseLayout = document.getElementById('denseLayout').classList.contains('active');

            // Reset classes
            previewTable.className = 'preview-table';

            // Apply row height
            if (rowHeight <= 28) {
                previewTable.classList.add('row-compact');
            } else if (rowHeight >= 40) {
                previewTable.classList.add('row-comfortable');
            }

            // Apply font size
            if (fontSize <= 11) {
                previewTable.classList.add('font-small');
            } else if (fontSize >= 14) {
                previewTable.classList.add('font-large');
            }

            // Apply grid lines
            if (gridLines === 'none') {
                previewTable.classList.add('no-borders');
            } else if (gridLines === 'horizontal') {
                previewTable.classList.add('horizontal-only');
            } else if (gridLines === 'vertical') {
                previewTable.classList.add('vertical-only');
            }

            // Apply striped rows
            if (stripedRows) {
                previewTable.classList.add('striped');
            }

            // Apply custom styles for exact values
            previewTable.style.fontSize = fontSize + 'px';
            const cells = previewTable.querySelectorAll('td, th');
            cells.forEach(cell => {
                if (denseLayout) {
                    cell.style.padding = '4px 8px';
                } else {
                    const padding = Math.max(4, Math.floor(rowHeight / 4));
                    cell.style.padding = padding + 'px ' + (padding * 1.5) + 'px';
                }
            });
        }

        function toggleAppearance(toggleId) {
            const toggle = document.getElementById(toggleId);
            toggle.classList.toggle('active');
        }

        function saveEditConfiguration() {
            const configKey = window.currentEditingConfig;
            if (!configKey) return;

            // Get selected columns
            const checkboxes = document.querySelectorAll('#editColumnsGrid .column-checkbox');
            const selectedColumns = [];

            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedColumns.push(checkbox.getAttribute('data-column'));
                }
            });

            // Update configuration
            if (selectedColumns.length > 0) {
                gridConfigurations[configKey].columns = selectedColumns;
                gridConfigurations[configKey].description = `${selectedColumns.length} visible columns`;

                // Get appearance settings
                const rowHeight = parseInt(document.getElementById('rowHeightSlider').value);
                const fontSize = parseInt(document.getElementById('fontSizeSlider').value);
                const gridLines = document.getElementById('gridLines').value;
                const stripedRows = document.getElementById('stripedRows').classList.contains('active');
                const hoverEffects = document.getElementById('hoverEffects').classList.contains('active');
                const denseLayout = document.getElementById('denseLayout').classList.contains('active');

                // Store appearance settings
                if (!gridConfigurations[configKey].appearance) {
                    gridConfigurations[configKey].appearance = {};
                }
                gridConfigurations[configKey].appearance = {
                    rowHeight,
                    fontSize,
                    gridLines,
                    stripedRows,
                    hoverEffects,
                    denseLayout
                };

                // Update the grid if this is the current configuration
                if (currentGridConfig === configKey) {
                    renderGridViewWithConfig(configKey);
                }

                // Update configurations list
                updateConfigurationsList();

                console.log(`Updated configuration: ${gridConfigurations[configKey].name}`);
            }

            closeEditConfig();
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
