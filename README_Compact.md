# Compact Workorder Dashboard - Google Material Design

A space-efficient, Google Material Design-inspired workorder dashboard built with pure HTML, CSS, and JavaScript.

## 🎯 Key Features

### Compact Header Design
- **Minimal Space Usage**: All controls fit in a compact 2-row header
- **Queue Counters**: My Queue (15) and All Queue (127) with click functionality
- **Action Buttons**: Export All, Collapse All, Take All
- **Status Pills**: All 8 status types with counts in compact pill format

### Status Types with Counts
- **Created** (23) - Blue theme
- **Move to Tech** (18) - Purple theme  
- **In Progress** (35) - Orange theme
- **Assign To Parts Specialist** (12) - Green theme
- **Completed** (89) - Teal theme
- **Hold** (8) - Red theme
- **Pending For Quote Approval** (14) - Amber theme
- **Rejected Warranty Claims** (6) - Pink theme

### Data Grid
- **Clean Table Layout**: Professional workorder listing
- **Sortable Columns**: ID, Title, Status, Assigned To, Priority, Created Date
- **Interactive Actions**: Edit and View buttons for each workorder
- **Status Filtering**: Click status pills to filter workorders
- **Priority Indicators**: Color-coded priority levels

## 🎨 Design Principles

### Google Material Design
- **Clean Typography**: Google Sans font family
- **Subtle Shadows**: Material elevation system
- **Rounded Corners**: 8px border radius for cards
- **Color System**: Material Design color palette
- **Interactive States**: Hover and click feedback

### Space Efficiency
- **Compact Pills**: Status indicators use minimal space
- **Dense Layout**: Maximum information in minimal height
- **Responsive Grid**: Adapts to screen size
- **Efficient Typography**: Optimized font sizes

## 🚀 Interactive Features

### Queue Management
- **My Queue Filter**: Click to show only personal workorders
- **All Queue Filter**: Click to show all workorders
- **Real-time Counts**: Updates every 30 seconds

### Status Filtering
- **Click to Filter**: Click any status pill to filter workorders
- **Visual Feedback**: Selected status highlighted
- **Count Display**: Each status shows current count

### Action Buttons
- **Export All**: Export workorder data
- **Collapse All**: Collapse expanded sections
- **Take All**: Assign all workorders to current user

### Table Actions
- **Edit Workorder**: Modify workorder details
- **View Workorder**: View full workorder information
- **Status Pills**: Visual status indicators in table

## 📱 Responsive Design

### Desktop (1200px+)
- Full header layout with all elements visible
- Wide table with all columns
- Optimal spacing and typography

### Tablet (768px - 1199px)
- Stacked header rows
- Responsive table layout
- Touch-friendly buttons

### Mobile (< 768px)
- Vertical layout
- Centered elements
- Mobile-optimized interactions

## 🔧 Customization

### Adding New Status Types
```javascript
const statusConfig = [
    { name: 'New Status', class: 'status-new', shortName: 'New' },
    // Add to statusConfig array
];

// Add corresponding CSS class
.status-new { 
    background: #e8f5e8; 
    color: #2e7d32; 
    border-color: #c8e6c9; 
}
.status-new .status-dot { background: #2e7d32; }
```

### Modifying Colors
Update the CSS custom properties for each status:
```css
.status-created { 
    background: #e3f2fd; 
    color: #1565c0; 
    border-color: #bbdefb; 
}
```

### Adding Table Columns
Modify the table structure in `generateWorkorderTable()`:
```javascript
row.innerHTML = `
    <td>${workorder.id}</td>
    <td>${workorder.newColumn}</td>
    // Add new columns here
`;
```

## 📊 Data Structure

### Dashboard Data
```javascript
const dashboardData = {
    myQueue: 15,
    allQueue: 127,
    statusCounts: {
        'Created': 23,
        'Move to Tech': 18,
        // ... other statuses
    }
};
```

### Workorder Structure
```javascript
{
    id: 'WO-001',
    title: 'Fix printer issue',
    status: 'Created',
    assignedTo: 'John Doe',
    priority: 'High',
    createdDate: '2024-01-15'
}
```

## 🔄 Real-time Updates

- **Auto-refresh**: Queue counts update every 30 seconds
- **Simulated Changes**: Random count variations
- **Visual Feedback**: Loading states and animations
- **Notification System**: Toast notifications for actions

## 🎯 Performance

- **Lightweight**: Single HTML file under 15KB
- **Fast Rendering**: Efficient DOM manipulation
- **Smooth Animations**: CSS transitions for interactions
- **Responsive**: Optimized for all screen sizes

## 🚀 Usage

1. **Open File**: Double-click `WorkorderDashboard.html`
2. **Filter Data**: Click queue counters or status pills
3. **Manage Workorders**: Use Edit/View buttons
4. **Export Data**: Click Export All button

## 🔌 Integration

### API Connection
Replace mock data with API calls:
```javascript
async function fetchWorkorders() {
    const response = await fetch('/api/workorders');
    return response.json();
}
```

### Database Integration
Connect to your workorder management system:
```javascript
function updateWorkorderStatus(id, status) {
    fetch(`/api/workorders/${id}`, {
        method: 'PUT',
        body: JSON.stringify({ status }),
        headers: { 'Content-Type': 'application/json' }
    });
}
```

Perfect for modern workorder management with minimal space consumption!
