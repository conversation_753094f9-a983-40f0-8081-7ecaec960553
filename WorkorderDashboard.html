<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workorder Dashboard</title>
    <style>
        /* Google Material Design inspired styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
           
            color: #202124;
            line-height: 1.5;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            transition: all 0.3s ease;
        }

        .main-layout.with-analytics {
            grid-template-columns: 1fr 350px;
        }

        .left-section {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .right-section {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        /* Modern Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            padding: 24px 32px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;
        }

        /* Queue Counters - Compact */
        .queue-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .queue-counter {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            background: #f1f3f4;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .queue-counter:hover {
            background: #e8eaed;
            transform: translateY(-1px);
        }

        .queue-counter.my-queue {
            background: #e3f2fd;
            color: #1565c0;
        }

        .queue-counter.all-queue {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .queue-counter.active {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }

        .queue-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            background: currentColor;
            color: white;
        }

        .queue-text {
            font-size: 12px;
            font-weight: 500;
        }

        .queue-count {
            font-weight: 700;
            font-size: 14px;
        }

        /* Status Pills - Compact */
        .status-section {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            align-items: center;
        }

        .status-pill {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .status-pill:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        /* Status Colors */
        .status-created {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #0d47a1;
            border-color: #2196f3;
        }
        .status-created .status-dot { background: #2196f3; }

        .status-move-tech {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #4a148c;
            border-color: #9c27b0;
        }
        .status-move-tech .status-dot { background: #9c27b0; }

        .status-in-progress {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            color: #e65100;
            border-color: #ff9800;
        }
        .status-in-progress .status-dot { background: #ff9800; }

        .status-parts {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #1b5e20;
            border-color: #4caf50;
        }
        .status-parts .status-dot { background: #4caf50; }

        .status-completed {
            background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
            color: #004d40;
            border-color: #00bcd4;
        }
        .status-completed .status-dot { background: #00bcd4; }

        .status-hold {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #b71c1c;
            border-color: #f44336;
        }
        .status-hold .status-dot { background: #f44336; }

        .status-quote {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            color: #e65100;
            border-color: #ff9800;
        }
        .status-quote .status-dot { background: #ff9800; }

        .status-rejected {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            color: #880e4f;
            border-color: #e91e63;
        }
        .status-rejected .status-dot { background: #e91e63; }

        /* Action Buttons */
        .actions-section {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            border: 2px solid transparent;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e8eaed 100%);
            color: #5f6368;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .action-btn:hover {
            background: black;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .action-btn.primary {
            background: black;
             color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .action-btn.primary:hover {
          background: black;
             color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }

        .action-btn.analytics-toggle {
            background: #34a853;
            color: white;
            border-color: #34a853;
            font-weight: 500;
        }

        .action-btn.analytics-toggle:hover {
            background: #2d8f47;
        }

        .action-btn.analytics-toggle.active {
            background: #1a73e8;
            border-color: #1a73e8;
        }

        .action-btn.analytics-toggle.active:hover {
            background: #1557b0;
        }

        /* View Toggle Buttons */
        .view-toggle-group {
            display: flex;
            border: 1px solid #dadce0;
            border-radius: 4px;
            overflow: hidden;
            margin-right: 8px;
        }

        .view-btn {
            padding: 4px 8px;
            border: none;
            background: white;
            color: #5f6368;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-right: 1px solid #dadce0;
            min-width: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .view-btn:last-child {
            border-right: none;
        }

        .view-btn:hover {
            background: #f8f9fa;
        }

        .view-btn.active {
            background: #1a73e8;
            color: white;
        }

        .view-btn.active:hover {
            background: #1557b0;
        }

        /* Legend Cards - Ultra Compact */
        .legend-cards {
            display: grid;
            gap: 8px;
            margin-bottom: 12px;
        }

        .legend-cards.my-queue {
            grid-template-columns: 1fr;
        }

        .legend-cards.all-queue {
            grid-template-columns: 1fr 1fr;
        }

        .legend-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .legend-card-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 16px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 600;
            font-size: 14px;
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .legend-card-content {
            padding: 8px;
        }

        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 4px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            min-height: 24px;
        }

        .legend-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .legend-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .legend-count {
            margin-left: auto;
            font-weight: 700;
            font-size: 12px;
            min-width: 20px;
            text-align: right;
        }

        .legend-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
        }

        /* Legend Item Colors - Job Card Status */
        .legend-created { background: #f8f9fa; color: #202124; border-color: #e0e0e0; }
        .legend-created .legend-dot { background: #6c757d; }

        .legend-moved-tech { background: #1976d2; color: white; }
        .legend-moved-tech .legend-dot { background: white; }

        .legend-in-progress { background: #ffc107; color: #212529; }
        .legend-in-progress .legend-dot { background: #212529; }

        .legend-job-completed { background: #28a745; color: white; }
        .legend-job-completed .legend-dot { background: white; }

        .legend-completed { background: #17a2b8; color: white; }
        .legend-completed .legend-dot { background: white; }

        .legend-parts-specialist { background: #6f42c1; color: white; }
        .legend-parts-specialist .legend-dot { background: white; }

        .legend-hold { background: #e91e63; color: white; }
        .legend-hold .legend-dot { background: white; }

        .legend-closed { background: #20c997; color: white; }
        .legend-closed .legend-dot { background: white; }

        .legend-quote-approval { background: #00bcd4; color: white; }
        .legend-quote-approval .legend-dot { background: white; }

        .legend-rejected { background: #dc3545; color: white; }
        .legend-rejected .legend-dot { background: white; }

        /* Historical Legend Colors */
        .legend-customer-invoice { background: #ff9800; color: white; }
        .legend-customer-invoice .legend-dot { background: white; }

        .legend-internal-invoice { background: #9c27b0; color: white; }
        .legend-internal-invoice .legend-dot { background: white; }

        .legend-warranty-claim { background: #2196f3; color: white; }
        .legend-warranty-claim .legend-dot { background: white; }

        /* Modern Data Grid */
        .grid-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .grid-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 20px 32px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .grid-content {
            min-height: 400px;
            padding: 16px;
        }

        .grid-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e8eaed;
        }

        .grid-table th {
            background:black;
            color: white;
            font-weight: 600;
            padding: 20px 24px;
            text-align: left;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            position: sticky;
            top: 0;
            z-index: 10;
            border: none;
        }

        .grid-table th:first-child {
            border-top-left-radius: 12px;
        }

        .grid-table th:last-child {
            border-top-right-radius: 12px;
        }

        .grid-table td {
            
            font-size: 14px;
            border-bottom: 1px solid #f5f7fa;
            color: #2c3e50;
            font-weight: 500;
            vertical-align: middle;
        }

        .grid-table tbody tr {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid #f5f7fa;
        }

        .grid-table tbody tr:nth-child(even) {
            background: linear-gradient(135deg, #fafbff 0%, #f8f9ff 100%);
        }

        .grid-table tbody tr:hover {
            background: linear-gradient(135deg, #e8f2ff 0%, #e1efff 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .grid-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 12px;
        }

        .grid-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 12px;
        }

        /* Grid View Styles */
        .grid-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 12px;
            padding: 16px;
        }

        .grid-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .grid-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .grid-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }

        .grid-item-title {
            font-weight: 600;
            color: #1a73e8;
            font-size: 14px;
        }

        .grid-item-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .grid-item-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 12px;
        }

        .grid-field {
            display: flex;
            flex-direction: column;
        }

        .grid-field-label {
            color: #5f6368;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 2px;
        }

        .grid-field-value {
            color: #202124;
            font-weight: 500;
        }

        /* List View Styles */
        .list-view {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 16px;
        }

        .list-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 12px 16px;
            display: grid;
            grid-template-columns: 120px 1fr 100px 120px 80px;
            gap: 16px;
            align-items: center;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .list-item:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
        }

        .list-item-wo {
            font-weight: 600;
            color: #1a73e8;
            font-size: 14px;
        }

        .list-item-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .list-item-customer {
            font-weight: 500;
            color: #202124;
            font-size: 13px;
        }

        .list-item-vin {
            color: #5f6368;
            font-size: 11px;
            font-family: monospace;
        }

        .list-item-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-align: center;
        }

        .list-item-dates {
            display: flex;
            flex-direction: column;
            gap: 2px;
            font-size: 11px;
        }

        .list-item-actions {
            display: flex;
            gap: 4px;
        }

        .list-action-btn {
            padding: 4px 8px;
            border: 1px solid #dadce0;
            border-radius: 3px;
            background: white;
            color: #5f6368;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .list-action-btn:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
            color: #1a73e8;
        }

        /* Card View Styles */
        .card-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 16px;
            padding: 16px;
        }

        .card-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .card-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8eaed 100%);
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
        }

        .card-title {
            font-weight: 600;
            color: #1a73e8;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .card-subtitle {
            color: #5f6368;
            font-size: 12px;
        }

        .card-content {
            padding: 16px;
        }

        .card-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 12px;
        }

        .card-row:last-child {
            margin-bottom: 0;
        }

        .card-field {
            display: flex;
            flex-direction: column;
        }

        .card-field-label {
            color: #5f6368;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .card-field-value {
            color: #202124;
            font-weight: 500;
            font-size: 13px;
        }

        .card-status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }

        .card-status {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .card-action-btn {
            padding: 6px 12px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: white;
            color: #5f6368;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .card-action-btn:hover {
            background: #1a73e8;
            border-color: #1a73e8;
            color: white;
        }

        /* Status Colors */
        .status-created { background: #f8f9fa; color: #202124; }
        .status-in-progress { background: #fff3e0; color: #ef6c00; }
        .status-completed { background: #e8f5e8; color: #2e7d32; }
        .status-hold { background: #ffebee; color: #c62828; }

        /* Additional Utility Classes */
        .badge {
            background: #e8eaed;
            color: #5f6368;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        code {
            background: #f8f9fa;
            color: #d73a49;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }

        /* View Content Containers */
        .view-content {
            min-height: 400px;
        }

        /* Responsive adjustments for views */
        @media (max-width: 768px) {
            .grid-view {
                grid-template-columns: 1fr;
            }

            .card-view {
                grid-template-columns: 1fr;
            }

            .list-item {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .list-item-details {
                order: 1;
            }

            .list-item-wo {
                order: 0;
            }

            .list-item-status {
                order: 2;
            }

            .list-item-dates {
                order: 3;
            }

            .list-item-actions {
                order: 4;
            }
        }

        /* Charts Card */
        .charts-card {
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .charts-header {
            background: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
            font-size: 13px;
            color: #202124;
        }

        .charts-content {
            padding: 12px;
        }

        .chart-section {
            margin-bottom: 20px;
        }

        .chart-section:last-child {
            margin-bottom: 0;
        }

        .chart-title {
            font-size: 12px;
            font-weight: 600;
            color: #5f6368;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Queue Charts */
        .queue-chart {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .queue-chart-item {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            border-radius: 6px;
            position: relative;
        }

        .queue-chart-item.my-queue {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
        }

        .queue-chart-item.all-queue {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
        }

        .queue-chart-value {
            font-size: 24px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 4px;
        }

        .queue-chart-label {
            font-size: 10px;
            font-weight: 500;
            opacity: 0.8;
        }

        /* Donut Chart */
        .donut-chart {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 16px;
        }

        .donut-chart svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .donut-chart-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .donut-chart-total {
            font-size: 18px;
            font-weight: 700;
            color: #202124;
            line-height: 1;
        }

        .donut-chart-label {
            font-size: 9px;
            color: #5f6368;
            font-weight: 500;
        }

        /* Bar Chart */
        .bar-chart {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .bar-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .bar-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            flex-shrink: 0;
        }

        .bar-label {
            flex: 1;
            font-size: 10px;
            color: #202124;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .bar-value {
            font-size: 11px;
            font-weight: 600;
            color: #5f6368;
            min-width: 20px;
            text-align: right;
        }

        .bar-progress {
            width: 60px;
            height: 4px;
            background: #f1f3f4;
            border-radius: 2px;
            overflow: hidden;
            margin-left: 4px;
        }

        .bar-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.8s ease;
        }

        /* Status Colors for Charts */
        .status-created-chart { background: #6c757d; }
        .status-moved-tech-chart { background: #1976d2; }
        .status-in-progress-chart { background: #ffc107; }
        .status-job-completed-chart { background: #28a745; }
        .status-completed-chart { background: #17a2b8; }
        .status-parts-specialist-chart { background: #6f42c1; }
        .status-hold-chart { background: #e91e63; }
        .status-closed-chart { background: #20c997; }
        .status-quote-approval-chart { background: #00bcd4; }
        .status-rejected-chart { background: #dc3545; }

        /* Pie Chart for My Queue */
        .pie-chart {
            position: relative;
            width: 140px;
            height: 140px;
            margin: 0 auto 16px;
        }

        .pie-chart svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .pie-chart-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .pie-chart-total {
            font-size: 20px;
            font-weight: 700;
            color: #202124;
            line-height: 1;
        }

        .pie-chart-label {
            font-size: 10px;
            color: #5f6368;
            font-weight: 500;
        }

        /* Stacked Bar Chart for All Queue */
        .stacked-bar-chart {
            margin-bottom: 16px;
        }

        .stacked-bar {
            width: 100%;
            height: 20px;
            background: #f1f3f4;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            margin-bottom: 12px;
        }

        .stacked-segment {
            height: 100%;
            transition: all 0.3s ease;
            position: relative;
        }

        .stacked-segment:hover {
            opacity: 0.8;
        }

        /* Priority Chart */
        .priority-chart {
            display: flex;
            justify-content: space-between;
            gap: 8px;
            margin-bottom: 16px;
        }

        .priority-item {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            border-radius: 6px;
            position: relative;
        }

        .priority-item.high {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #c62828;
        }

        .priority-item.medium {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            color: #ef6c00;
        }

        .priority-item.low {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
        }

        .priority-value {
            font-size: 18px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 4px;
        }

        .priority-label {
            font-size: 9px;
            font-weight: 500;
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-layout.with-analytics {
                grid-template-columns: 1fr 300px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 6px;
            }

            .main-layout,
            .main-layout.with-analytics {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .header-row {
                flex-direction: column;
                align-items: stretch;
            }

            .queue-section,
            .actions-section {
                justify-content: center;
            }

            .legend-cards {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .legend-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 3px;
            }

            .legend-item {
                padding: 3px 5px;
                font-size: 10px;
                min-height: 22px;
            }

            .legend-card-content {
                padding: 6px;
            }

            .legend-card-header {
                padding: 5px 8px;
                font-size: 11px;
            }

            .donut-chart {
                width: 100px;
                height: 100px;
            }

            .queue-chart-value {
                font-size: 20px;
            }
        }

        /* Extra compact for very small screens */
        @media (max-width: 480px) {
            .legend-grid {
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
                gap: 2px;
            }

            .legend-item {
                padding: 2px 4px;
                font-size: 9px;
                min-height: 20px;
            }

            .legend-text {
                font-size: 9px;
            }

            .legend-count {
                font-size: 10px;
                min-width: 18px;
            }

            .legend-dot {
                width: 5px;
                height: 5px;
            }
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Compact Header -->
        <div class="header">
            <!-- First Row: Queue Counters -->
            <div class="header-row">
                <div class="queue-section">
                    <div class="queue-counter my-queue active" onclick="filterQueue('my')" id="myQueueBtn">
                        <div class="queue-icon">👤</div>
                        <div class="queue-text">My Queue</div>
                        <div class="queue-count" id="myQueueCount">15</div>
                    </div>
                    <div class="queue-counter all-queue" onclick="filterQueue('all')" id="allQueueBtn">
                        <div class="queue-icon">📋</div>
                        <div class="queue-text">All Queue</div>
                        <div class="queue-count" id="allQueueCount">127</div>
                    </div>
                </div>

                <div class="actions-section">
                    <div class="view-toggle-group">
                        <button class="view-btn active" onclick="switchView('grid')" id="gridViewBtn" title="Grid View">⊞</button>
                        <button class="view-btn" onclick="switchView('list')" id="listViewBtn" title="List View">☰</button>
                        <button class="view-btn" onclick="switchView('card')" id="cardViewBtn" title="Card View">⊡</button>
                    </div>
                    <button class="action-btn" onclick="exportData()">Export All</button>
                    <button class="action-btn" onclick="collapseAll()">Collapse All</button>
                    <button class="action-btn primary" onclick="takeAll()">Take All</button>
                    <button class="action-btn analytics-toggle" onclick="toggleAnalytics()" id="analyticsToggle">
                        📊 Analytics
                    </button>
                </div>
            </div>


        </div>

        <!-- Main Layout with Charts -->
        <div class="main-layout">
            <!-- Left Section -->
            <div class="left-section">
                <!-- Legend Cards -->
                <div class="legend-cards my-queue" id="legendCards" style="display: grid;">
                    <!-- Job Card Status Legend -->
                    <div class="legend-card" id="jobCardLegend">
                        <div class="legend-card-header">
                            <span>Job Card Status Legend</span>
                        </div>
                        <div class="legend-card-content">
                            <div class="legend-grid" id="jobCardGrid">
                                <!-- Job card status items will be generated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Historical Legend -->
                    <div class="legend-card" id="historicalLegend" style="display: none;">
                        <div class="legend-card-header">
                            <span>Historical Legend</span>
                        </div>
                        <div class="legend-card-content">
                            <div class="legend-grid" id="historicalGrid">
                                <!-- Historical legend items will be generated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Views -->
                <div class="grid-container">
                    <div class="grid-header">
                        Workorder List
                    </div>
                        <div class="actions-section">
                    <div class="view-toggle-group">
                        <button class="view-btn active" onclick="switchView('grid')" id="gridViewBtn" title="Grid View">⊞</button>
                        <button class="view-btn" onclick="switchView('list')" id="listViewBtn" title="List View">☰</button>
                        <!-- <button class="view-btn" onclick="switchView('card')" id="cardViewBtn" title="Card View">⊡</button> -->
                    </div>
                    </div>
                    <div class="grid-content">
                        <!-- Grid View (Table) -->
                        <div id="gridView" class="view-content">
                            <table class="grid-table" id="workorderTable">
                                <thead>
                                    <tr>
                                        <th>Work Order #</th>
                                        <th>Customer Account #</th>
                                        <th>Customer Name</th>
                                        <th>VIN #</th>
                                        <th>Chassis #</th>
                                        <th>Quotation #</th>
                                        <th>Ticket #</th>
                                        <th>Planned Start Date</th>
                                        <th>Planned Completion Date</th>
                                        <th>Unit #</th>
                                        <th>Door Code #</th>
                                        <th>Is Drop in?</th>
                                        <th>Key Tag #</th>
                                        <th>Bay</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <!-- Table rows will be generated here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- List View -->
                        <div id="listView" class="view-content list-view" style="display: none;">
                            <!-- List items will be generated here -->
                        </div>

                        <!-- Card View -->
                        <div id="cardView" class="view-content card-view" style="display: none;">
                            <!-- Card items will be generated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Section - Charts -->
            <div class="right-section" id="analyticsPanel" style="display: none;">
                <!-- Charts Card -->
                <div class="charts-card">
                    <div class="charts-header">
                        📊 Analytics Dashboard
                    </div>
                    <div class="charts-content">
                        <!-- Queue Overview Chart -->
                        <div class="chart-section">
                            <div class="chart-title">Queue Overview</div>
                            <div class="queue-chart">
                                <div class="queue-chart-item my-queue">
                                    <div class="queue-chart-value" id="chartMyQueue">15</div>
                                    <div class="queue-chart-label">My Queue</div>
                                </div>
                                <div class="queue-chart-item all-queue">
                                    <div class="queue-chart-value" id="chartAllQueue">127</div>
                                    <div class="queue-chart-label">All Queue</div>
                                </div>
                            </div>
                        </div>

                        <!-- My Queue Chart Section -->
                        <div class="chart-section" id="myQueueSection">
                            <div class="chart-title">My Queue Analytics</div>
                            <div class="pie-chart" id="myPieChart">
                                <svg viewBox="0 0 42 42">
                                    <!-- Pie segments will be generated here -->
                                </svg>
                                <div class="pie-chart-center">
                                    <div class="pie-chart-total" id="myQueueTotal">15</div>
                                    <div class="pie-chart-label">My Tasks</div>
                                </div>
                            </div>
                            <div class="chart-title">My Priority Breakdown</div>
                            <div class="priority-chart">
                                <div class="priority-item high">
                                    <div class="priority-value">5</div>
                                    <div class="priority-label">High</div>
                                </div>
                                <div class="priority-item medium">
                                    <div class="priority-value">7</div>
                                    <div class="priority-label">Medium</div>
                                </div>
                                <div class="priority-item low">
                                    <div class="priority-value">3</div>
                                    <div class="priority-label">Low</div>
                                </div>
                            </div>
                        </div>

                        <!-- All Queue Chart Section -->
                        <div class="chart-section" id="allQueueSection">
                            <div class="chart-title">All Queue Analytics</div>
                            <div class="stacked-bar-chart">
                                <div class="stacked-bar" id="stackedBar"></div>
                            </div>
                            <div class="chart-title">Department Workload</div>
                            <div class="priority-chart">
                                <div class="priority-item high">
                                    <div class="priority-value">45</div>
                                    <div class="priority-label">Tech Team</div>
                                </div>
                                <div class="priority-item medium">
                                    <div class="priority-value">38</div>
                                    <div class="priority-label">Parts Team</div>
                                </div>
                                <div class="priority-item low">
                                    <div class="priority-value">44</div>
                                    <div class="priority-label">Admin Team</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dashboard Data
        const dashboardData = {
            myQueue: 15,
            allQueue: 127,
            statusCounts: {
                'Created': 23,
                'Move to Tech': 18,
                'In Progress': 35,
                'Assign To Parts Specialist': 12,
                'Completed': 89,
                'Hold': 8,
                'Pending For Quote Approval': 14,
                'Rejected Warranty Claims': 6
            },
            myQueueData: {
                'In Progress': 8,
                'Created': 4,
                'Hold': 2,
                'Move to Tech': 1
            },
            allQueueData: {
                'Created': 23,
                'Move to Tech': 18,
                'In Progress': 35,
                'Job Completed by Tech': 25,
                'Completed': 89,
                'Assign To Parts Specialist': 12,
                'Hold': 8,
                'Closed': 15,
                'Pending For Quote Approval': 14,
                'Rejected Warranty Claims': 6
            }
        };

        const statusConfig = [
            { name: 'Created', class: 'status-created', shortName: 'Created' },
            { name: 'Move to Tech', class: 'status-move-tech', shortName: 'Move to Tech' },
            { name: 'In Progress', class: 'status-in-progress', shortName: 'In Progress' },
            { name: 'Assign To Parts Specialist', class: 'status-parts', shortName: 'Parts Specialist' },
            { name: 'Completed', class: 'status-completed', shortName: 'Completed' },
            { name: 'Hold', class: 'status-hold', shortName: 'Hold' },
            { name: 'Pending For Quote Approval', class: 'status-quote', shortName: 'Quote Approval' },
            { name: 'Rejected Warranty Claims', class: 'status-rejected', shortName: 'Rejected' }
        ];

        // Job Card Status Legend Data
        const jobCardLegend = [
            { name: 'Created', class: 'legend-created', count: 23 },
            { name: 'Moved to Tech', class: 'legend-moved-tech', count: 18 },
            { name: 'In Progress', class: 'legend-in-progress', count: 35 },
            { name: 'Job Completed by Tech', class: 'legend-job-completed', count: 25 },
            { name: 'Completed', class: 'legend-completed', count: 89 },
            { name: 'Assign To Parts Specialist', class: 'legend-parts-specialist', count: 12 },
            { name: 'Hold', class: 'legend-hold', count: 8 },
            { name: 'Closed', class: 'legend-closed', count: 15 },
            { name: 'Pending For Quote Approval', class: 'legend-quote-approval', count: 14 },
            { name: 'Rejected Warranty Claims', class: 'legend-rejected', count: 6 }
        ];

        // Historical Legend Data
        const historicalLegend = [
            { name: 'Customer Invoice - Completed', class: 'legend-customer-invoice', count: 45 },
            { name: 'Internal Invoice - Completed', class: 'legend-internal-invoice', count: 32 },
            { name: 'Warranty Claim - Completed', class: 'legend-warranty-claim', count: 28 }
        ];

        // Sample workorder data with all columns
        const workorders = [
            {
                workOrderNumber: 'WO-001',
                customerAccount: 'CUST-12345',
                customerName: 'ABC Manufacturing Corp',
                vinNumber: '1HGBH41JXMN109186',
                chassisNumber: 'CH-789456',
                quotationNumber: 'QT-2024-001',
                ticketNumber: 'TK-445566',
                plannedStartDate: '2024-01-15',
                plannedCompletionDate: '2024-01-20',
                unitNumber: 'UNIT-001',
                doorCode: 'DC-A1B2',
                isDropIn: 'Yes',
                keyTag: 'KEY-789',
                bay: 'Bay-A1',
                status: 'Created',
                priority: 'High'
            },
            {
                workOrderNumber: 'WO-002',
                customerAccount: 'CUST-67890',
                customerName: 'XYZ Logistics Ltd',
                vinNumber: '2T1BURHE0JC123456',
                chassisNumber: 'CH-123789',
                quotationNumber: 'QT-2024-002',
                ticketNumber: 'TK-778899',
                plannedStartDate: '2024-01-16',
                plannedCompletionDate: '2024-01-22',
                unitNumber: 'UNIT-002',
                doorCode: 'DC-C3D4',
                isDropIn: 'No',
                keyTag: 'KEY-456',
                bay: 'Bay-B2',
                status: 'In Progress',
                priority: 'Medium'
            },
            {
                workOrderNumber: 'WO-003',
                customerAccount: 'CUST-11111',
                customerName: 'Tech Solutions Inc',
                vinNumber: '3VWDP7AJ5DM123789',
                chassisNumber: 'CH-456123',
                quotationNumber: 'QT-2024-003',
                ticketNumber: 'TK-112233',
                plannedStartDate: '2024-01-17',
                plannedCompletionDate: '2024-01-25',
                unitNumber: 'UNIT-003',
                doorCode: 'DC-E5F6',
                isDropIn: 'Yes',
                keyTag: 'KEY-123',
                bay: 'Bay-C3',
                status: 'Completed',
                priority: 'Low'
            },
            {
                workOrderNumber: 'WO-004',
                customerAccount: 'CUST-22222',
                customerName: 'Global Services Co',
                vinNumber: '4T1BF1FK5DU789456',
                chassisNumber: 'CH-789123',
                quotationNumber: 'QT-2024-004',
                ticketNumber: 'TK-445577',
                plannedStartDate: '2024-01-18',
                plannedCompletionDate: '2024-01-24',
                unitNumber: 'UNIT-004',
                doorCode: 'DC-G7H8',
                isDropIn: 'No',
                keyTag: 'KEY-987',
                bay: 'Bay-D4',
                status: 'Hold',
                priority: 'High'
            },
            {
                workOrderNumber: 'WO-005',
                customerAccount: 'CUST-33333',
                customerName: 'Premier Auto Group',
                vinNumber: '5NPE34AF5FH456789',
                chassisNumber: 'CH-321654',
                quotationNumber: 'QT-2024-005',
                ticketNumber: 'TK-998877',
                plannedStartDate: '2024-01-19',
                plannedCompletionDate: '2024-01-26',
                unitNumber: 'UNIT-005',
                doorCode: 'DC-I9J0',
                isDropIn: 'Yes',
                keyTag: 'KEY-654',
                bay: 'Bay-E5',
                status: 'In Progress',
                priority: 'Medium'
            },
            {
                workOrderNumber: 'WO-006',
                customerAccount: 'CUST-44444',
                customerName: 'Metro Transport Services',
                vinNumber: '1FTFW1ET5DFC12345',
                chassisNumber: 'CH-987321',
                quotationNumber: 'QT-2024-006',
                ticketNumber: 'TK-556677',
                plannedStartDate: '2024-01-20',
                plannedCompletionDate: '2024-01-28',
                unitNumber: 'UNIT-006',
                doorCode: 'DC-K1L2',
                isDropIn: 'No',
                keyTag: 'KEY-321',
                bay: 'Bay-F6',
                status: 'Move to Tech',
                priority: 'High'
            },
            {
                workOrderNumber: 'WO-007',
                customerAccount: 'CUST-55555',
                customerName: 'Rapid Delivery Corp',
                vinNumber: '2HKRM4H75GH789456',
                chassisNumber: 'CH-654987',
                quotationNumber: 'QT-2024-007',
                ticketNumber: 'TK-334455',
                plannedStartDate: '2024-01-21',
                plannedCompletionDate: '2024-01-30',
                unitNumber: 'UNIT-007',
                doorCode: 'DC-M3N4',
                isDropIn: 'Yes',
                keyTag: 'KEY-789',
                bay: 'Bay-G7',
                status: 'Parts Specialist',
                priority: 'Medium'
            },
            {
                workOrderNumber: 'WO-008',
                customerAccount: 'CUST-66666',
                customerName: 'Elite Logistics Ltd',
                vinNumber: '3C6UR5CL8GG123789',
                chassisNumber: 'CH-147258',
                quotationNumber: 'QT-2024-008',
                ticketNumber: 'TK-667788',
                plannedStartDate: '2024-01-22',
                plannedCompletionDate: '2024-02-01',
                unitNumber: 'UNIT-008',
                doorCode: 'DC-O5P6',
                isDropIn: 'No',
                keyTag: 'KEY-456',
                bay: 'Bay-H8',
                status: 'Pending For Quote Approval',
                priority: 'Low'
            },
            {
                workOrderNumber: 'WO-009',
                customerAccount: 'CUST-77777',
                customerName: 'Swift Cargo Solutions',
                vinNumber: '1GCCS14W5R8456123',
                chassisNumber: 'CH-369852',
                quotationNumber: 'QT-2024-009',
                ticketNumber: 'TK-889900',
                plannedStartDate: '2024-01-23',
                plannedCompletionDate: '2024-02-02',
                unitNumber: 'UNIT-009',
                doorCode: 'DC-Q7R8',
                isDropIn: 'Yes',
                keyTag: 'KEY-147',
                bay: 'Bay-I9',
                status: 'Rejected Warranty Claims',
                priority: 'High'
            },
            {
                workOrderNumber: 'WO-010',
                customerAccount: 'CUST-88888',
                customerName: 'Professional Fleet Management',
                vinNumber: '4JGDA5HB8DA789654',
                chassisNumber: 'CH-258147',
                quotationNumber: 'QT-2024-010',
                ticketNumber: 'TK-112244',
                plannedStartDate: '2024-01-24',
                plannedCompletionDate: '2024-02-05',
                unitNumber: 'UNIT-010',
                doorCode: 'DC-S9T0',
                isDropIn: 'No',
                keyTag: 'KEY-852',
                bay: 'Bay-J10',
                status: 'Job Completed by Tech',
                priority: 'Medium'
            }
        ];

        let currentFilter = 'all';
        let currentView = 'grid';

        // Initialize Dashboard
        function initializeDashboard() {
            updateQueueCounters();
            generateWorkorderTable();

            // Set up default My Queue view
            generateJobCardLegend();

            // Set up real-time updates
            setInterval(updateDashboard, 30000);
        }

        // Update Queue Counters
        function updateQueueCounters() {
            document.getElementById('myQueueCount').textContent = dashboardData.myQueue;
            document.getElementById('allQueueCount').textContent = dashboardData.allQueue;

            // Update chart counters
            document.getElementById('chartMyQueue').textContent = dashboardData.myQueue;
            document.getElementById('chartAllQueue').textContent = dashboardData.allQueue;
        }

        // Generate Charts - Always show both
        function generateCharts() {
            generateMyPieChart();
            generateStackedBarChart();
        }

        // Generate My Queue Pie Chart
        function generateMyPieChart() {
            const svg = document.querySelector('#myPieChart svg');
            const data = dashboardData.myQueueData;
            const total = Object.values(data).reduce((sum, count) => sum + count, 0);

            let cumulativePercentage = 0;
            const radius = 15.91549430918954;
            const colors = ['#ffc107', '#6c757d', '#e91e63', '#1976d2'];

            svg.innerHTML = '';

            Object.entries(data).forEach(([status, count], index) => {
                const percentage = (count / total) * 100;
                const strokeDasharray = `${percentage} ${100 - percentage}`;
                const strokeDashoffset = -cumulativePercentage;

                const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                circle.setAttribute('cx', '21');
                circle.setAttribute('cy', '21');
                circle.setAttribute('r', radius);
                circle.setAttribute('fill', 'transparent');
                circle.setAttribute('stroke', colors[index]);
                circle.setAttribute('stroke-width', '4');
                circle.setAttribute('stroke-dasharray', strokeDasharray);
                circle.setAttribute('stroke-dashoffset', strokeDashoffset);

                svg.appendChild(circle);
                cumulativePercentage += percentage;
            });
        }





        // Generate Workorder Views
        function generateWorkorderTable() {
            if (currentView === 'grid') {
                generateGridView();
            } else if (currentView === 'list') {
                generateListView();
            } else if (currentView === 'card') {
                generateCardView();
            }
        }

        // Generate Grid View (Table)
        function generateGridView() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            let filteredWorkorders = workorders;

            // Apply filters if needed
            if (currentFilter !== 'all') {
                filteredWorkorders = workorders.filter(wo => wo.status === currentFilter);
            }

            filteredWorkorders.forEach(workorder => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td><strong>${workorder.workOrderNumber}</strong></td>
                    <td>${workorder.customerAccount}</td>
                    <td>${workorder.customerName}</td>
                    <td><code>${workorder.vinNumber}</code></td>
                    <td>${workorder.chassisNumber}</td>
                    <td>${workorder.quotationNumber}</td>
                    <td>${workorder.ticketNumber}</td>
                    <td>${formatDate(workorder.plannedStartDate)}</td>
                    <td>${formatDate(workorder.plannedCompletionDate)}</td>
                    <td>${workorder.unitNumber}</td>
                    <td>${workorder.doorCode}</td>
                    <td><span class="badge ${workorder.isDropIn === 'Yes' ? 'badge-success' : 'badge-secondary'}">${workorder.isDropIn}</span></td>
                    <td>${workorder.keyTag}</td>
                    <td><span class="badge">${workorder.bay}</span></td>
                    <td>
                        <div class="status-pill status-${workorder.status.toLowerCase().replace(/\s+/g, '-')}" style="display: inline-flex;">
                            <div class="status-dot"></div>
                            <span>${workorder.status}</span>
                        </div>
                    </td>
                    <td>
                        <button class="action-btn" onclick="editWorkorder('${workorder.workOrderNumber}')">Edit</button>
                        <button class="action-btn" onclick="viewWorkorder('${workorder.workOrderNumber}')">View</button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Generate List View
        function generateListView() {
            const listView = document.getElementById('listView');
            listView.innerHTML = '';

            let filteredWorkorders = workorders;

            // Apply filters if needed
            if (currentFilter !== 'all') {
                filteredWorkorders = workorders.filter(wo => wo.status === currentFilter);
            }

            filteredWorkorders.forEach(workorder => {
                const listItem = document.createElement('div');
                listItem.className = 'list-item';

                listItem.innerHTML = `
                    <div class="list-item-wo">${workorder.workOrderNumber}</div>
                    <div class="list-item-details">
                        <div class="list-item-customer">${workorder.customerName}</div>
                        <div class="list-item-vin">VIN: ${workorder.vinNumber}</div>
                    </div>
                    <div class="list-item-status status-${workorder.status.toLowerCase().replace(/\s+/g, '-')}">${workorder.status}</div>
                    <div class="list-item-dates">
                        <div>Start: ${formatDate(workorder.plannedStartDate)}</div>
                        <div>End: ${formatDate(workorder.plannedCompletionDate)}</div>
                    </div>
                    <div class="list-item-actions">
                        <button class="list-action-btn" onclick="editWorkorder('${workorder.workOrderNumber}')">Edit</button>
                        <button class="list-action-btn" onclick="viewWorkorder('${workorder.workOrderNumber}')">View</button>
                    </div>
                `;

                listView.appendChild(listItem);
            });
        }

        // Generate Card View
        function generateCardView() {
            const cardView = document.getElementById('cardView');
            cardView.innerHTML = '';

            let filteredWorkorders = workorders;

            // Apply filters if needed
            if (currentFilter !== 'all') {
                filteredWorkorders = workorders.filter(wo => wo.status === currentFilter);
            }

            filteredWorkorders.forEach(workorder => {
                const cardItem = document.createElement('div');
                cardItem.className = 'card-item';

                cardItem.innerHTML = `
                    <div class="card-header">
                        <div class="card-title">${workorder.workOrderNumber}</div>
                        <div class="card-subtitle">${workorder.customerName}</div>
                    </div>
                    <div class="card-content">
                        <div class="card-row">
                            <div class="card-field">
                                <div class="card-field-label">Customer Account</div>
                                <div class="card-field-value">${workorder.customerAccount}</div>
                            </div>
                            <div class="card-field">
                                <div class="card-field-label">VIN Number</div>
                                <div class="card-field-value">${workorder.vinNumber}</div>
                            </div>
                        </div>
                        <div class="card-row">
                            <div class="card-field">
                                <div class="card-field-label">Chassis Number</div>
                                <div class="card-field-value">${workorder.chassisNumber}</div>
                            </div>
                            <div class="card-field">
                                <div class="card-field-label">Quotation Number</div>
                                <div class="card-field-value">${workorder.quotationNumber}</div>
                            </div>
                        </div>
                        <div class="card-row">
                            <div class="card-field">
                                <div class="card-field-label">Ticket Number</div>
                                <div class="card-field-value">${workorder.ticketNumber}</div>
                            </div>
                            <div class="card-field">
                                <div class="card-field-label">Unit Number</div>
                                <div class="card-field-value">${workorder.unitNumber}</div>
                            </div>
                        </div>
                        <div class="card-row">
                            <div class="card-field">
                                <div class="card-field-label">Planned Start Date</div>
                                <div class="card-field-value">${formatDate(workorder.plannedStartDate)}</div>
                            </div>
                            <div class="card-field">
                                <div class="card-field-label">Planned Completion</div>
                                <div class="card-field-value">${formatDate(workorder.plannedCompletionDate)}</div>
                            </div>
                        </div>
                        <div class="card-row">
                            <div class="card-field">
                                <div class="card-field-label">Door Code</div>
                                <div class="card-field-value">${workorder.doorCode}</div>
                            </div>
                            <div class="card-field">
                                <div class="card-field-label">Is Drop In?</div>
                                <div class="card-field-value">${workorder.isDropIn}</div>
                            </div>
                        </div>
                        <div class="card-row">
                            <div class="card-field">
                                <div class="card-field-label">Key Tag</div>
                                <div class="card-field-value">${workorder.keyTag}</div>
                            </div>
                            <div class="card-field">
                                <div class="card-field-label">Bay</div>
                                <div class="card-field-value">${workorder.bay}</div>
                            </div>
                        </div>
                        <div class="card-status-row">
                            <div class="card-status status-${workorder.status.toLowerCase().replace(/\s+/g, '-')}">${workorder.status}</div>
                            <div class="card-actions">
                                <button class="card-action-btn" onclick="editWorkorder('${workorder.workOrderNumber}')">Edit</button>
                                <button class="card-action-btn" onclick="viewWorkorder('${workorder.workOrderNumber}')">View</button>
                            </div>
                        </div>
                    </div>
                `;

                cardView.appendChild(cardItem);
            });
        }

        // Helper Functions
        function getStatusConfig(statusName) {
            return statusConfig.find(s => s.name === statusName) || statusConfig[0];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        // Generate Legend Cards
        function generateJobCardLegend() {
            const jobCardGrid = document.getElementById('jobCardGrid');
            jobCardGrid.innerHTML = '';

            jobCardLegend.forEach(item => {
                const legendItem = document.createElement('div');
                legendItem.className = `legend-item ${item.class}`;
                legendItem.onclick = () => filterByLegendStatus(item.name);

                legendItem.innerHTML = `
                    <div class="legend-dot"></div>
                    <span class="legend-text">${item.name}</span>
                    <span class="legend-count">${item.count}</span>
                `;

                jobCardGrid.appendChild(legendItem);
            });
        }

        function generateHistoricalLegend() {
            const historicalGrid = document.getElementById('historicalGrid');
            historicalGrid.innerHTML = '';

            historicalLegend.forEach(item => {
                const legendItem = document.createElement('div');
                legendItem.className = `legend-item ${item.class}`;
                legendItem.onclick = () => filterByLegendStatus(item.name);

                legendItem.innerHTML = `
                    <div class="legend-dot"></div>
                    <span class="legend-text">${item.name}</span>
                    <span class="legend-count">${item.count}</span>
                `;

                historicalGrid.appendChild(legendItem);
            });
        }

        // Generate Stacked Bar Chart for All Queue
        function generateStackedBarChart() {
            const stackedBar = document.getElementById('stackedBar');
            const data = dashboardData.allQueueData;
            const total = Object.values(data).reduce((sum, count) => sum + count, 0);

            const colors = [
                '#6c757d', '#1976d2', '#ffc107', '#28a745', '#17a2b8',
                '#6f42c1', '#e91e63', '#20c997', '#00bcd4', '#dc3545'
            ];

            stackedBar.innerHTML = '';

            Object.entries(data).forEach(([status, count], index) => {
                const percentage = (count / total) * 100;

                const segment = document.createElement('div');
                segment.className = 'stacked-segment';
                segment.style.width = `${percentage}%`;
                segment.style.background = colors[index];
                segment.title = `${status}: ${count} (${percentage.toFixed(1)}%)`;

                stackedBar.appendChild(segment);
            });
        }



        // Filter Functions
        function filterQueue(type) {
            const legendCards = document.getElementById('legendCards');
            const jobCardLegend = document.getElementById('jobCardLegend');
            const historicalLegend = document.getElementById('historicalLegend');
            const myQueueBtn = document.getElementById('myQueueBtn');
            const allQueueBtn = document.getElementById('allQueueBtn');

            // Update active states
            myQueueBtn.classList.remove('active');
            allQueueBtn.classList.remove('active');

            if (type === 'my') {
                // My Queue: Show only Job Card Status Legend
                myQueueBtn.classList.add('active');
                legendCards.style.display = 'grid';
                legendCards.className = 'legend-cards my-queue';
                jobCardLegend.style.display = 'block';
                historicalLegend.style.display = 'none';
                generateJobCardLegend();
                showNotification('Showing My Queue - Job Card Status Legend');
            } else {
                // All Queue: Show both Job Card Status Legend and Historical Legend
                allQueueBtn.classList.add('active');
                legendCards.style.display = 'grid';
                legendCards.className = 'legend-cards all-queue';
                jobCardLegend.style.display = 'block';
                historicalLegend.style.display = 'block';
                generateJobCardLegend();
                generateHistoricalLegend();
                showNotification('Showing All Queue - Job Card Status & Historical Legends');
            }

            currentFilter = 'all';
            generateWorkorderTable();
            // Charts remain visible always - no need to regenerate
        }

        function filterByLegendStatus(status) {
            console.log('Filtering by legend status:', status);
            showNotification(`Filtering by: ${status}`);
            // Add filtering logic here
        }

        function filterByStatus(status) {
            currentFilter = status;
            generateWorkorderTable();

            // Update visual feedback
            document.querySelectorAll('.status-pill').forEach(pill => {
                pill.style.opacity = '0.5';
            });
            event.target.closest('.status-pill').style.opacity = '1';
        }

        // Action Functions
        function exportData() {
            console.log('Exporting data...');
            showNotification('Data exported successfully!');
        }

        function collapseAll() {
            console.log('Collapsing all...');
            showNotification('All sections collapsed');
        }

        function takeAll() {
            console.log('Taking all workorders...');
            showNotification('All workorders assigned to you');
        }

        function toggleAnalytics() {
            const analyticsPanel = document.getElementById('analyticsPanel');
            const mainLayout = document.querySelector('.main-layout');
            const toggleBtn = document.getElementById('analyticsToggle');

            if (analyticsPanel.style.display === 'none') {
                // Show analytics
                analyticsPanel.style.display = 'flex';
                mainLayout.classList.add('with-analytics');
                toggleBtn.classList.add('active');
                toggleBtn.innerHTML = '📊 Hide Analytics';
                showNotification('Analytics dashboard opened');

                // Generate charts when panel is shown
                generateCharts();
            } else {
                // Hide analytics
                analyticsPanel.style.display = 'none';
                mainLayout.classList.remove('with-analytics');
                toggleBtn.classList.remove('active');
                toggleBtn.innerHTML = '📊 Analytics';
                showNotification('Analytics dashboard closed');
            }
        }

        // Switch View Function
        function switchView(viewType) {
            // Update current view
            currentView = viewType;

            // Update button states
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(viewType + 'ViewBtn').classList.add('active');

            // Hide all views
            document.getElementById('gridView').style.display = 'none';
            document.getElementById('listView').style.display = 'none';
            document.getElementById('cardView').style.display = 'none';

            // Show selected view
            if (viewType === 'grid') {
                document.getElementById('gridView').style.display = 'block';
            } else if (viewType === 'list') {
                document.getElementById('listView').style.display = 'block';
            } else if (viewType === 'card') {
                document.getElementById('cardView').style.display = 'block';
            }

            // Regenerate content for the new view
            generateWorkorderTable();

            // Show notification
            showNotification(`Switched to ${viewType} view`);
        }

        function editWorkorder(id) {
            console.log('Editing workorder:', id);
            showNotification(`Editing workorder ${id}`);
        }

        function viewWorkorder(id) {
            console.log('Viewing workorder:', id);
            showNotification(`Viewing workorder ${id}`);
        }

        // Update Dashboard
        function updateDashboard() {
            // Simulate data changes
            dashboardData.myQueue += Math.floor(Math.random() * 3) - 1;
            dashboardData.allQueue += Math.floor(Math.random() * 5) - 2;

            dashboardData.myQueue = Math.max(0, dashboardData.myQueue);
            dashboardData.allQueue = Math.max(0, dashboardData.allQueue);

            // Simulate status count changes
            Object.keys(dashboardData.statusCounts).forEach(status => {
                const change = Math.floor(Math.random() * 3) - 1;
                dashboardData.statusCounts[status] = Math.max(0, dashboardData.statusCounts[status] + change);
            });

            // Simulate my queue data changes
            Object.keys(dashboardData.myQueueData).forEach(status => {
                const change = Math.floor(Math.random() * 2) - 1;
                dashboardData.myQueueData[status] = Math.max(0, dashboardData.myQueueData[status] + change);
            });

            // Simulate all queue data changes
            Object.keys(dashboardData.allQueueData).forEach(status => {
                const change = Math.floor(Math.random() * 3) - 1;
                dashboardData.allQueueData[status] = Math.max(0, dashboardData.allQueueData[status] + change);
            });

            updateQueueCounters();

            // Only update charts if analytics panel is visible
            const analyticsPanel = document.getElementById('analyticsPanel');
            if (analyticsPanel.style.display !== 'none') {
                generateCharts();
            }

            // Update legend cards if they are visible
            const legendCards = document.getElementById('legendCards');
            if (legendCards.style.display !== 'none') {
                generateJobCardLegend();
                if (document.getElementById('historicalLegend').style.display !== 'none') {
                    generateHistoricalLegend();
                }
            }
        }

        // Show Notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1a73e8;
                color: white;
                padding: 12px 16px;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-size: 14px;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .priority-high { color: #c62828; font-weight: 600; }
            .priority-medium { color: #ef6c00; font-weight: 500; }
            .priority-low { color: #2e7d32; font-weight: 500; }
        `;
        document.head.appendChild(style);

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>
