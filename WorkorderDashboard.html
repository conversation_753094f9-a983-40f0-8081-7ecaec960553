<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workorder Dashboard</title>
    <style>
        /* Google Material Design inspired styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', '<PERSON>o', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #202124;
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 16px;
        }

        /* Compact Header */
        .header {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            margin-bottom: 16px;
            padding: 12px 16px;
        }

        .header-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 8px;
        }

        .header-row:last-child {
            margin-bottom: 0;
        }

        /* Queue Counters - Compact */
        .queue-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .queue-counter {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #f1f3f4;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .queue-counter:hover {
            background: #e8eaed;
            transform: translateY(-1px);
        }

        .queue-counter.my-queue {
            background: #e3f2fd;
            color: #1565c0;
        }

        .queue-counter.all-queue {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .queue-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            background: currentColor;
            color: white;
        }

        .queue-text {
            font-size: 14px;
            font-weight: 500;
        }

        .queue-count {
            font-weight: 700;
            font-size: 16px;
        }

        /* Status Pills - Compact */
        .status-section {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            align-items: center;
        }

        .status-pill {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .status-pill:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        /* Status Colors */
        .status-created { background: #e3f2fd; color: #1565c0; border-color: #bbdefb; }
        .status-created .status-dot { background: #1565c0; }

        .status-move-tech { background: #f3e5f5; color: #7b1fa2; border-color: #e1bee7; }
        .status-move-tech .status-dot { background: #7b1fa2; }

        .status-in-progress { background: #fff3e0; color: #ef6c00; border-color: #ffcc02; }
        .status-in-progress .status-dot { background: #ef6c00; }

        .status-parts { background: #e8f5e8; color: #2e7d32; border-color: #c8e6c9; }
        .status-parts .status-dot { background: #2e7d32; }

        .status-completed { background: #e0f2f1; color: #00695c; border-color: #b2dfdb; }
        .status-completed .status-dot { background: #00695c; }

        .status-hold { background: #ffebee; color: #c62828; border-color: #ffcdd2; }
        .status-hold .status-dot { background: #c62828; }

        .status-quote { background: #fff8e1; color: #f57c00; border-color: #ffecb3; }
        .status-quote .status-dot { background: #f57c00; }

        .status-rejected { background: #fce4ec; color: #ad1457; border-color: #f8bbd9; }
        .status-rejected .status-dot { background: #ad1457; }

        /* Action Buttons */
        .actions-section {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            background: white;
            color: #3c4043;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f8f9fa;
            border-color: #dadce0;
        }

        .action-btn.primary {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
        }

        .action-btn.primary:hover {
            background: #1557b0;
        }

        /* Legend Cards */
        .legend-cards {
            display: grid;
            gap: 12px;
            margin-bottom: 16px;
        }

        .legend-cards.my-queue {
            grid-template-columns: 1fr;
        }

        .legend-cards.all-queue {
            grid-template-columns: 1fr 1fr;
        }

        .legend-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .legend-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.16), 0 2px 4px rgba(0,0,0,0.12);
            transform: translateY(-1px);
        }

        .legend-card-header {
            background: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
            font-size: 13px;
            color: #202124;
        }

        .legend-card-content {
            padding: 12px;
        }

        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 6px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .legend-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .legend-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .legend-count {
            margin-left: auto;
            font-weight: 700;
            font-size: 13px;
        }

        .legend-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Legend Item Colors - Job Card Status */
        .legend-created { background: #f8f9fa; color: #202124; border-color: #e0e0e0; }
        .legend-created .legend-dot { background: #6c757d; }

        .legend-moved-tech { background: #1976d2; color: white; }
        .legend-moved-tech .legend-dot { background: white; }

        .legend-in-progress { background: #ffc107; color: #212529; }
        .legend-in-progress .legend-dot { background: #212529; }

        .legend-job-completed { background: #28a745; color: white; }
        .legend-job-completed .legend-dot { background: white; }

        .legend-completed { background: #17a2b8; color: white; }
        .legend-completed .legend-dot { background: white; }

        .legend-parts-specialist { background: #6f42c1; color: white; }
        .legend-parts-specialist .legend-dot { background: white; }

        .legend-hold { background: #e91e63; color: white; }
        .legend-hold .legend-dot { background: white; }

        .legend-closed { background: #20c997; color: white; }
        .legend-closed .legend-dot { background: white; }

        .legend-quote-approval { background: #00bcd4; color: white; }
        .legend-quote-approval .legend-dot { background: white; }

        .legend-rejected { background: #dc3545; color: white; }
        .legend-rejected .legend-dot { background: white; }

        /* Historical Legend Colors */
        .legend-customer-invoice { background: #ff9800; color: white; }
        .legend-customer-invoice .legend-dot { background: white; }

        .legend-internal-invoice { background: #9c27b0; color: white; }
        .legend-internal-invoice .legend-dot { background: white; }

        .legend-warranty-claim { background: #2196f3; color: white; }
        .legend-warranty-claim .legend-dot { background: white; }

        /* Data Grid */
        .grid-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            overflow: hidden;
        }

        .grid-header {
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
            font-size: 14px;
            color: #5f6368;
        }

        .grid-content {
            min-height: 400px;
            padding: 16px;
        }

        .grid-table {
            width: 100%;
            border-collapse: collapse;
        }

        .grid-table th {
            text-align: left;
            padding: 12px 8px;
            font-weight: 500;
            font-size: 13px;
            color: #5f6368;
            border-bottom: 1px solid #e0e0e0;
        }

        .grid-table td {
            padding: 12px 8px;
            font-size: 14px;
            border-bottom: 1px solid #f0f0f0;
        }

        .grid-table tr:hover {
            background: #f8f9fa;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 8px;
            }

            .header-row {
                flex-direction: column;
                align-items: stretch;
            }

            .queue-section,
            .status-section,
            .actions-section {
                justify-content: center;
            }

            .status-section {
                justify-content: flex-start;
            }

            .legend-cards {
                grid-template-columns: 1fr;
            }

            .legend-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Compact Header -->
        <div class="header">
            <!-- First Row: Queue Counters -->
            <div class="header-row">
                <div class="queue-section">
                    <div class="queue-counter my-queue" onclick="filterQueue('my')">
                        <div class="queue-icon">👤</div>
                        <div class="queue-text">My Queue</div>
                        <div class="queue-count" id="myQueueCount">15</div>
                    </div>
                    <div class="queue-counter all-queue" onclick="filterQueue('all')">
                        <div class="queue-icon">📋</div>
                        <div class="queue-text">All Queue</div>
                        <div class="queue-count" id="allQueueCount">127</div>
                    </div>
                </div>

                <div class="actions-section">
                    <button class="action-btn" onclick="exportData()">Export All</button>
                    <button class="action-btn" onclick="collapseAll()">Collapse All</button>
                    <button class="action-btn primary" onclick="takeAll()">Take All</button>
                </div>
            </div>


        </div>

        <!-- Legend Cards -->
        <div class="legend-cards my-queue" id="legendCards" style="display: none;">
            <!-- Job Card Status Legend -->
            <div class="legend-card" id="jobCardLegend">
                <div class="legend-card-header">
                    <span>Job Card Status Legend</span>
                </div>
                <div class="legend-card-content">
                    <div class="legend-grid" id="jobCardGrid">
                        <!-- Job card status items will be generated here -->
                    </div>
                </div>
            </div>

            <!-- Historical Legend -->
            <div class="legend-card" id="historicalLegend" style="display: none;">
                <div class="legend-card-header">
                    <span>Historical Legend</span>
                </div>
                <div class="legend-card-content">
                    <div class="legend-grid" id="historicalGrid">
                        <!-- Historical legend items will be generated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Grid -->
        <div class="grid-container">
            <div class="grid-header">
                Workorder List
            </div>
            <div class="grid-content">
                <table class="grid-table" id="workorderTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Status</th>
                            <th>Assigned To</th>
                            <th>Priority</th>
                            <th>Created Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table rows will be generated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Dashboard Data
        const dashboardData = {
            myQueue: 15,
            allQueue: 127,
            statusCounts: {
                'Created': 23,
                'Move to Tech': 18,
                'In Progress': 35,
                'Assign To Parts Specialist': 12,
                'Completed': 89,
                'Hold': 8,
                'Pending For Quote Approval': 14,
                'Rejected Warranty Claims': 6
            }
        };

        const statusConfig = [
            { name: 'Created', class: 'status-created', shortName: 'Created' },
            { name: 'Move to Tech', class: 'status-move-tech', shortName: 'Move to Tech' },
            { name: 'In Progress', class: 'status-in-progress', shortName: 'In Progress' },
            { name: 'Assign To Parts Specialist', class: 'status-parts', shortName: 'Parts Specialist' },
            { name: 'Completed', class: 'status-completed', shortName: 'Completed' },
            { name: 'Hold', class: 'status-hold', shortName: 'Hold' },
            { name: 'Pending For Quote Approval', class: 'status-quote', shortName: 'Quote Approval' },
            { name: 'Rejected Warranty Claims', class: 'status-rejected', shortName: 'Rejected' }
        ];

        // Job Card Status Legend Data
        const jobCardLegend = [
            { name: 'Created', class: 'legend-created', count: 23 },
            { name: 'Moved to Tech', class: 'legend-moved-tech', count: 18 },
            { name: 'In Progress', class: 'legend-in-progress', count: 35 },
            { name: 'Job Completed by Tech', class: 'legend-job-completed', count: 25 },
            { name: 'Completed', class: 'legend-completed', count: 89 },
            { name: 'Assign To Parts Specialist', class: 'legend-parts-specialist', count: 12 },
            { name: 'Hold', class: 'legend-hold', count: 8 },
            { name: 'Closed', class: 'legend-closed', count: 15 },
            { name: 'Pending For Quote Approval', class: 'legend-quote-approval', count: 14 },
            { name: 'Rejected Warranty Claims', class: 'legend-rejected', count: 6 }
        ];

        // Historical Legend Data
        const historicalLegend = [
            { name: 'Customer Invoice - Completed', class: 'legend-customer-invoice', count: 45 },
            { name: 'Internal Invoice - Completed', class: 'legend-internal-invoice', count: 32 },
            { name: 'Warranty Claim - Completed', class: 'legend-warranty-claim', count: 28 }
        ];

        // Sample workorder data
        const workorders = [
            { id: 'WO-001', title: 'Fix printer issue', status: 'Created', assignedTo: 'John Doe', priority: 'High', createdDate: '2024-01-15' },
            { id: 'WO-002', title: 'Network connectivity problem', status: 'In Progress', assignedTo: 'Jane Smith', priority: 'Medium', createdDate: '2024-01-14' },
            { id: 'WO-003', title: 'Software installation', status: 'Move to Tech', assignedTo: 'Mike Johnson', priority: 'Low', createdDate: '2024-01-13' },
            { id: 'WO-004', title: 'Hardware replacement', status: 'Assign To Parts Specialist', assignedTo: 'Sarah Wilson', priority: 'High', createdDate: '2024-01-12' },
            { id: 'WO-005', title: 'System maintenance', status: 'Completed', assignedTo: 'Tom Brown', priority: 'Medium', createdDate: '2024-01-11' },
            { id: 'WO-006', title: 'Security update', status: 'Hold', assignedTo: 'Lisa Davis', priority: 'High', createdDate: '2024-01-10' },
            { id: 'WO-007', title: 'Equipment repair', status: 'Pending For Quote Approval', assignedTo: 'Chris Lee', priority: 'Medium', createdDate: '2024-01-09' },
            { id: 'WO-008', title: 'Warranty claim', status: 'Rejected Warranty Claims', assignedTo: 'Amy Taylor', priority: 'Low', createdDate: '2024-01-08' }
        ];

        let currentFilter = 'all';

        // Initialize Dashboard
        function initializeDashboard() {
            updateQueueCounters();
            generateWorkorderTable();

            // Set up real-time updates
            setInterval(updateDashboard, 30000);
        }

        // Update Queue Counters
        function updateQueueCounters() {
            document.getElementById('myQueueCount').textContent = dashboardData.myQueue;
            document.getElementById('allQueueCount').textContent = dashboardData.allQueue;
        }



        // Generate Workorder Table
        function generateWorkorderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            let filteredWorkorders = workorders;

            // Apply filters if needed
            if (currentFilter !== 'all') {
                filteredWorkorders = workorders.filter(wo => wo.status === currentFilter);
            }

            filteredWorkorders.forEach(workorder => {
                const row = document.createElement('tr');
                const statusConfig = getStatusConfig(workorder.status);

                row.innerHTML = `
                    <td><strong>${workorder.id}</strong></td>
                    <td>${workorder.title}</td>
                    <td>
                        <div class="status-pill ${statusConfig.class}" style="display: inline-flex;">
                            <div class="status-dot"></div>
                            <span>${workorder.status}</span>
                        </div>
                    </td>
                    <td>${workorder.assignedTo}</td>
                    <td>
                        <span class="priority-${workorder.priority.toLowerCase()}">${workorder.priority}</span>
                    </td>
                    <td>${formatDate(workorder.createdDate)}</td>
                    <td>
                        <button class="action-btn" onclick="editWorkorder('${workorder.id}')">Edit</button>
                        <button class="action-btn" onclick="viewWorkorder('${workorder.id}')">View</button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Helper Functions
        function getStatusConfig(statusName) {
            return statusConfig.find(s => s.name === statusName) || statusConfig[0];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        // Generate Legend Cards
        function generateJobCardLegend() {
            const jobCardGrid = document.getElementById('jobCardGrid');
            jobCardGrid.innerHTML = '';

            jobCardLegend.forEach(item => {
                const legendItem = document.createElement('div');
                legendItem.className = `legend-item ${item.class}`;
                legendItem.onclick = () => filterByLegendStatus(item.name);

                legendItem.innerHTML = `
                    <div class="legend-dot"></div>
                    <span class="legend-text">${item.name}</span>
                    <span class="legend-count">${item.count}</span>
                `;

                jobCardGrid.appendChild(legendItem);
            });
        }

        function generateHistoricalLegend() {
            const historicalGrid = document.getElementById('historicalGrid');
            historicalGrid.innerHTML = '';

            historicalLegend.forEach(item => {
                const legendItem = document.createElement('div');
                legendItem.className = `legend-item ${item.class}`;
                legendItem.onclick = () => filterByLegendStatus(item.name);

                legendItem.innerHTML = `
                    <div class="legend-dot"></div>
                    <span class="legend-text">${item.name}</span>
                    <span class="legend-count">${item.count}</span>
                `;

                historicalGrid.appendChild(legendItem);
            });
        }

        // Filter Functions
        function filterQueue(type) {
            const legendCards = document.getElementById('legendCards');
            const jobCardLegend = document.getElementById('jobCardLegend');
            const historicalLegend = document.getElementById('historicalLegend');

            if (type === 'my') {
                // My Queue: Show only Job Card Status Legend
                legendCards.style.display = 'grid';
                legendCards.className = 'legend-cards my-queue';
                jobCardLegend.style.display = 'block';
                historicalLegend.style.display = 'none';
                generateJobCardLegend();
                showNotification('Showing My Queue - Job Card Status Legend');
            } else {
                // All Queue: Show both Job Card Status Legend and Historical Legend
                legendCards.style.display = 'grid';
                legendCards.className = 'legend-cards all-queue';
                jobCardLegend.style.display = 'block';
                historicalLegend.style.display = 'block';
                generateJobCardLegend();
                generateHistoricalLegend();
                showNotification('Showing All Queue - Job Card Status & Historical Legends');
            }

            currentFilter = 'all';
            generateWorkorderTable();
        }

        function filterByLegendStatus(status) {
            console.log('Filtering by legend status:', status);
            showNotification(`Filtering by: ${status}`);
            // Add filtering logic here
        }

        function filterByStatus(status) {
            currentFilter = status;
            generateWorkorderTable();

            // Update visual feedback
            document.querySelectorAll('.status-pill').forEach(pill => {
                pill.style.opacity = '0.5';
            });
            event.target.closest('.status-pill').style.opacity = '1';
        }

        // Action Functions
        function exportData() {
            console.log('Exporting data...');
            showNotification('Data exported successfully!');
        }

        function collapseAll() {
            console.log('Collapsing all...');
            showNotification('All sections collapsed');
        }

        function takeAll() {
            console.log('Taking all workorders...');
            showNotification('All workorders assigned to you');
        }

        function editWorkorder(id) {
            console.log('Editing workorder:', id);
            showNotification(`Editing workorder ${id}`);
        }

        function viewWorkorder(id) {
            console.log('Viewing workorder:', id);
            showNotification(`Viewing workorder ${id}`);
        }

        // Update Dashboard
        function updateDashboard() {
            // Simulate data changes
            dashboardData.myQueue += Math.floor(Math.random() * 3) - 1;
            dashboardData.allQueue += Math.floor(Math.random() * 5) - 2;

            dashboardData.myQueue = Math.max(0, dashboardData.myQueue);
            dashboardData.allQueue = Math.max(0, dashboardData.allQueue);

            updateQueueCounters();

            // Update legend cards if they are visible
            const legendCards = document.getElementById('legendCards');
            if (legendCards.style.display !== 'none') {
                generateJobCardLegend();
                if (document.getElementById('historicalLegend').style.display !== 'none') {
                    generateHistoricalLegend();
                }
            }
        }

        // Show Notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1a73e8;
                color: white;
                padding: 12px 16px;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-size: 14px;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .priority-high { color: #c62828; font-weight: 600; }
            .priority-medium { color: #ef6c00; font-weight: 500; }
            .priority-low { color: #2e7d32; font-weight: 500; }
        `;
        document.head.appendChild(style);

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>
