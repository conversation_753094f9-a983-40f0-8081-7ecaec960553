.workorder-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 1rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  margin: 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.last-updated {
  color: #6b7280;
  font-size: 0.875rem;
  background: #f3f4f6;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
}

.dashboard-content {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
}

.queue-section, .status-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.queue-section h2, .status-section h2 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.queue-section h2::before {
  content: '📊';
  font-size: 1.5rem;
}

.status-section h2::before {
  content: '📈';
  font-size: 1.5rem;
}

.queue-counters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* Responsive Design */
@media (min-width: 768px) {
  .workorder-dashboard {
    padding: 2rem;
  }
  
  .dashboard-content {
    grid-template-columns: 1fr 2fr;
  }
  
  .queue-section {
    grid-column: 1;
  }
  
  .status-section {
    grid-column: 2;
  }
}

@media (min-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 350px 1fr;
  }
}

/* Animation */
.workorder-dashboard * {
  transition: all 0.3s ease;
}

.queue-section:hover, .status-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}
